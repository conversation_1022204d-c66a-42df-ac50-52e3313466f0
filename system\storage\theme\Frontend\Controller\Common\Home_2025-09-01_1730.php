<?php

namespace Theme25\Frontend\Controller\Common;

class Home extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'common/home');
    }

	public function index() {
		$this->setTitle($this->getConfig('config_name'));
		$this->addFrontendStyleWithVersion('swiper-bundle.min.css');
		$this->addFrontendScriptWithVersion('swiper-bundle.min.js', 'footer');

		// $this->setUnderConstructionWhenIsNotDeveloper(); // TODO: Да го махна, когато приключи разработката

		$hero_html = $this->loadController('theme/hero_banner');
		$this->setData('hero_banner_html', $hero_html);

		$popular_categories_html = $this->getPopularCategories();
		$this->setData('popular_categories_html', $popular_categories_html);

		// $featured_products_html = $this->loadController('theme/featured_products');
		// $this->setData('featured_products_html', $featured_products_html);

		// $promo_products_html = $this->loadController('theme/promo_products');
		// $this->setData('promo_products_html', $promo_products_html);

		// $collections_html = $this->loadController('theme/collections');
		// $this->setData('collections_html', $collections_html);

		$testimonials = true;
		$this->setData('testimonials', $testimonials);

		$benefits = true;
		$this->setData('benefits', $benefits);



		$this->renderTemplateWithDataAndOutput('common/home');
	}

	private function getPopularCategories() {

		$data = [];
		// .... в бъдеще ще може да се настройват от админ панела
		
		return $this->renderPartTemplate('common/home/<USER>', $data);
	}	
}
