<?php

namespace Theme25\Model\Catalog;

/**
 * Модел за работа с настройките на темата
 *
 * @package Theme25\Model\Catalog
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Information extends \Theme25\Model {

    public function __construct($registry) {
        parent::__construct($registry, $loadOriginalModel = true);
    }

    /**
     * Получаване на информация за страница
     */
    public function getInformation($information_id) {

        $sql = "SELECT DISTINCT i.*, id.*,
        ( SELECT keyword FROM " . DB_PREFIX . "seo_url su WHERE su.query = CONCAT('information_id=', i.information_id) AND su.language_id = '" . (int)$this->getLanguageId() . "' LIMIT 1 ) as keyword
        FROM " . DB_PREFIX . "information i
        LEFT JOIN " . DB_PREFIX . "information_description id ON (i.information_id = id.information_id)
        WHERE i.information_id = '" . (int)$information_id . "' AND id.language_id = '" . (int)$this->getLanguageId() . "'";
        
		$query = $this->dbQuery($sql);
        
		return $query->row;
	}

    /**
     * Получаване на многоезични данни за страница
     */
    public function getInformationDescriptions($information_id) {
        $descriptions = [];

        $query = $this->dbQuery("SELECT * FROM " . DB_PREFIX . "information_description WHERE information_id = '" . (int)$information_id . "'");

        foreach ($query->rows as $result) {
            $descriptions[$result['language_id']] = [
                'title' => $result['title'],
                'description' => $result['description'],
                'meta_title' => $result['meta_title'],
                'meta_description' => $result['meta_description'],
                'meta_keyword' => $result['meta_keyword']
            ];
        }

        return $descriptions;
    }

    /**
     * Получаване на SEO URL-та за всички езици
     */
    public function getInformationSeoUrls($information_id) {
        $seo_urls = [];

        $query = $this->dbQuery("SELECT language_id, keyword FROM " . DB_PREFIX . "seo_url WHERE query = 'information_id=" . (int)$information_id . "'");

        foreach ($query->rows as $result) {
            $seo_urls[$result['language_id']] = $result['keyword'];
        }

        return $seo_urls;
    }

    public function getInformations($data = []) {
        return $this->originalModel->getInformations($data);
    }

    public function getTotalInformations() {
        return $this->originalModel->getTotalInformations();
    }

    public function editInformation($information_id, $data) {

        $status = $data['status'] == 'on' ? 1 : 0;

        // Обновяване на основната информация
        $this->dbQuery("UPDATE " . DB_PREFIX . "information SET
            sort_order = '" . (int)($data['sort_order'] ?? 0) . "',
            status = '" . $status . "'
            WHERE information_id = '" . (int)$information_id . "'");

        // Изтриване на старите описания
        $this->dbQuery("DELETE FROM " . DB_PREFIX . "information_description WHERE information_id = '" . (int)$information_id . "'");

        // Добавяне на новите описания за всички езици
        if (isset($data['information_description'])) {
            foreach ($data['information_description'] as $language_id => $description) {
                $this->dbQuery("INSERT INTO " . DB_PREFIX . "information_description SET
                    information_id = '" . (int)$information_id . "',
                    language_id = '" . (int)$language_id . "',
                    title = '" . $this->dbEscape($description['title'] ?? '') . "',
                    description = '" . $this->dbEscape($description['description'] ?? '') . "',
                    meta_title = '" . $this->dbEscape($description['meta_title'] ?? '') . "',
                    meta_description = '" . $this->dbEscape($description['meta_description'] ?? '') . "',
                    meta_keyword = '" . $this->dbEscape($description['meta_keyword'] ?? '') . "'");
            }
        }

        // Обновяване на SEO URL-та
        if (isset($data['information_seo_url'])) {
            // Изтриване на старите SEO URL-та
            $this->dbQuery("DELETE FROM " . DB_PREFIX . "seo_url WHERE query = 'information_id=" . (int)$information_id . "'");

            // Добавяне на новите SEO URL-та
            foreach ($data['information_seo_url'] as $language_id => $keyword) {
                if (!empty($keyword)) {
                    $this->dbQuery("INSERT INTO " . DB_PREFIX . "seo_url SET
                        store_id = '0',
                        language_id = '" . (int)$language_id . "',
                        query = 'information_id=" . (int)$information_id . "',
                        keyword = '" . $this->dbEscape($keyword) . "'");
                }
            }
        }
    }

    /**
     * Добавяне на нова страница
     */
    public function addInformation($data) {

        $status = $data['status'] == 'on' ? 1 : 0;

        // Добавяне на основната информация
        $this->dbQuery("INSERT INTO " . DB_PREFIX . "information SET
            sort_order = '" . (int)($data['sort_order'] ?? 0) . "',
            status = '" . $status . "'");

        $information_id = $this->db->getLastId();

        // Добавяне на описанията за всички езици
        if (isset($data['information_description'])) {
            foreach ($data['information_description'] as $language_id => $description) {
                $this->dbQuery("INSERT INTO " . DB_PREFIX . "information_description SET
                    information_id = '" . (int)$information_id . "',
                    language_id = '" . (int)$language_id . "',
                    title = '" . $this->dbEscape($description['title'] ?? '') . "',
                    description = '" . $this->dbEscape($description['description'] ?? '') . "',
                    meta_title = '" . $this->dbEscape($description['meta_title'] ?? '') . "',
                    meta_description = '" . $this->dbEscape($description['meta_description'] ?? '') . "',
                    meta_keyword = '" . $this->dbEscape($description['meta_keyword'] ?? '') . "'");
            }
        }

        // Добавяне на SEO URL-та
        if (isset($data['information_seo_url'])) {
            foreach ($data['information_seo_url'] as $language_id => $keyword) {
                if (!empty($keyword)) {
                    $this->dbQuery("INSERT INTO " . DB_PREFIX . "seo_url SET
                        store_id = '0',
                        language_id = '" . (int)$language_id . "',
                        query = 'information_id=" . (int)$information_id . "',
                        keyword = '" . $this->dbEscape($keyword) . "'");
                }
            }
        }

        return $information_id;
    }

    /**
     * Търсене в информационни страници с case-insensitive заявка
     *
     * @param string $searchTerm Търсен термин
     * @param int $limit Максимален брой резултати
     * @param int $offset Отместване за пагинация
     * @return array Масив с резултати
     */
    public function searchInformations($searchTerm, $limit = 10, $offset = 0) {
        $sql = "SELECT i.information_id as id, id.title as name
                FROM " . DB_PREFIX . "information i
                LEFT JOIN " . DB_PREFIX . "information_description id ON (i.information_id = id.information_id)
                WHERE id.language_id = '" . (int)$this->getLanguageId() . "'
                AND i.status = '1'
                AND LOWER(id.title) LIKE '%" . $this->dbEscape(mb_strtolower($searchTerm)) . "%'
                ORDER BY id.title ASC
                LIMIT " . (int)$limit . " OFFSET " . (int)$offset;

        $query = $this->dbQuery($sql);

        return $query->rows;
    }

    /**
     * Броене на общия брой информационни страници за търсен термин
     *
     * @param string $searchTerm Търсен термин
     * @return int Общ брой резултати
     */
    public function countSearchInformations($searchTerm) {
        $sql = "SELECT COUNT(*) as total
                FROM " . DB_PREFIX . "information i
                LEFT JOIN " . DB_PREFIX . "information_description id ON (i.information_id = id.information_id)
                WHERE id.language_id = '" . (int)$this->getLanguageId() . "'
                AND i.status = '1'
                AND LOWER(id.title) LIKE '%" . $this->dbEscape(mb_strtolower($searchTerm)) . "%'";

        $query = $this->dbQuery($sql);

        return (int)$query->row['total'];
    }

}
