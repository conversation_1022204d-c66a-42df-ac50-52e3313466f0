<?php

namespace Theme25\Backend\Controller\Startup;

class Startup extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'startup/startup');
    }

    public function index() {
        // Settings
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "setting WHERE store_id = '0'");

        foreach ($query->rows as $setting) {
            if (!$setting['serialized']) {
                $this->config->set($setting['key'], $setting['value']);
            } else {
                $this->config->set($setting['key'], json_decode($setting['value'], true));
            }
        }

        // Theme
        $this->config->set('template_cache', $this->config->get('developer_theme'));
                
        // Language

        $config_admin_language = $this->config->get('config_admin_language') ? $this->config->get('config_admin_language') : 'bg-bg';
        $query = $this->db->query("SELECT * FROM `" . DB_PREFIX . "language` WHERE code = '" . $this->db->escape($config_admin_language) . "'");
        
        if ($query->num_rows) {
            $this->config->set('config_language_id', $query->row['language_id']);
        }

        // Language
        $language = new \Language($config_admin_language);
        $language->load($config_admin_language);
        $this->registry->set('language', $language);
    
        // Customer
        $this->registry->set('customer', new \Cart\Customer($this->registry));

        // Currency - използваме Theme25\Currency с поддръжка за двойни валути
        $this->registry->set('currency', new \Theme25\Currency($this->registry));
    
        // Tax
        $this->registry->set('tax', new \Cart\Tax($this->registry));
        
        if ($this->config->get('config_tax_default') == 'shipping') {
            $this->tax->setShippingAddress($this->config->get('config_country_id'), $this->config->get('config_zone_id'));
        }

        if ($this->config->get('config_tax_default') == 'payment') {
            $this->tax->setPaymentAddress($this->config->get('config_country_id'), $this->config->get('config_zone_id'));
        }

        $this->tax->setStoreAddress($this->config->get('config_country_id'), $this->config->get('config_zone_id'));

        // Weight
        $this->registry->set('weight', new \Cart\Weight($this->registry));
        
        // Length
        $this->registry->set('length', new \Cart\Length($this->registry));
        
        // Cart - използваме Theme25\Cart с адаптерен модел за Backend/Frontend
        $this->registry->set('cart', new \Theme25\Cart($this->registry));

        // OrderSession - за изолация на Backend операции от Frontend сесии
        // КОРЕКЦИЯ: Създаваме OrderSession само когато е необходимо
        if ($order_id = $this->shouldCreateOrderSession()) {
            $user_id = $this->user ? $this->user->getId() : 1;
            $this->registry->set('order_session', new \Theme25\OrderSession($this->db, $order_id, null, $user_id));
        }
        
        // Encryption
        $this->registry->set('encryption', new \Encryption($this->config->get('config_encryption')));
        
        // OpenBay Pro
        // $this->registry->set('openbay', new \Openbay($this->registry));
    }

    /**
     * Определя дали трябва да се създаде OrderSession
     * Предотвратява създаването на ненужни временни сесии за AJAX заявки
     */
    private function shouldCreateOrderSession() {
        $order_id = (int)$this->requestGet('order_id', 0);
        if(!$order_id) {
            $order_id = (int)$this->requestPost('order_id', 0);
        }
        return $order_id ? $order_id : false;
    }
}
