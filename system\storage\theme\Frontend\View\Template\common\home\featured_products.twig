{# Dynamic Featured Products #}
<section class="py-12">
  <div class="container mx-auto px-4">
    <h2 class="text-3xl font-bold text-center mb-10">{{ section_title }}</h2>

    {% if products is defined and products %}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {% for product in products %}
          <a href="{{ ' ' }}{{ product.href }}" class="block group">
            <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform transform hover:scale-105">
              <div class="relative">
                <div class="h-64 overflow-hidden">
                  <img src="{{ ' ' }}{{ product.thumb }}" alt="{{ ' ' }}{{ product.name }}" class="w-full h-full object-cover object-top transition-transform duration-300 group-hover:scale-110">
                </div>
                <div class="absolute top-2 right-2 w-8 h-8 flex items-center justify-center bg-white rounded-full shadow cursor-pointer hover:text-primary">
                  <i class="ri-heart-line ri-lg"></i>
                </div>
                {% if product.labels %}
                  <div class="absolute top-2 left-2 space-y-1">
                    {% for label in product.labels %}
                      <span class="{{ ' ' }}{{ label.class }}">{{ ' ' }}{{ label.text }}</span>
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="p-4">
                <h3 class="text-lg font-semibold mb-4 min-h-[56px] leading-7">{{ ' ' }}{{ product.name }}</h3>
                <div class="flex justify-between items-center">
                  <div class="flex flex-col">
                    {% if product.special is not null and product.special is not same as(false) %}
                      <span class="text-primary font-bold text-xl">{{ ' ' }}{{ product.special_formatted }}</span>
                      <span class="text-gray-400 line-through text-sm">{{ ' ' }}{{ product.price_formatted }}</span>
                    {% else %}
                      <span class="text-primary font-bold text-xl">{{ ' ' }}{{ product.price_formatted }}</span>
                    {% endif %}
                  </div>
                  <button class="bg-primary text-white px-6 py-2 rounded-button hover:bg-opacity-90 whitespace-nowrap buyButton">Купи</button>
                </div>
              </div>
            </div>
          </a>
        {% endfor %}
      </div>

      <div class="text-center mt-8">
        <a href="{{ ' ' }}{{ url_all_products|default('#') }}" class="inline-block bg-white border border-primary text-primary px-6 py-3 rounded-button font-medium hover:bg-primary hover:text-white transition-colors whitespace-nowrap">Виж всички продукти</a>
      </div>
    {% endif %}
  </div>
</section>