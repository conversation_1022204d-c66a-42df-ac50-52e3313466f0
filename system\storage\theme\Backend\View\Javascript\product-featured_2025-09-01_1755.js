(function(){
  'use strict';

  // Разширяваме BackendModule
  const ProductFeaturedModule = Object.create(BackendModule);

  Object.assign(ProductFeaturedModule, {
    config: {
      maxItems: 4,
      debounceMs: 300
    },

    init: function() {
      const search = document.getElementById('featured-search');
      const results = document.getElementById('featured-search-results');
      const selected = document.getElementById('featured-selected');
      const saveBtn = document.getElementById('featured-save');
      if(!search || !results || !selected || !saveBtn) return;

      this.state = {items: this.readSelected(selected)};

      // Търсене с debounce
      let t;
      search.addEventListener('input', (e) => {
        clearTimeout(t);
        const q = e.target.value.trim();
        if(q.length < 2){ results.classList.add('hidden'); results.innerHTML=''; return; }
        t = setTimeout(() => this.searchProducts(q, results), this.config.debounceMs);
      });

      // Премахване на избран
      selected.addEventListener('click', (e) => {
        const btn = e.target.closest('.remove-selected');
        if(!btn) return;
        const id = parseInt(btn.getAttribute('data-id'), 10);
        this.state.items = this.state.items.filter(i => i.product_id !== id);
        btn.closest('[data-id]')?.remove();
      });

      // Запис
      saveBtn.addEventListener('click', () => this.save());
    },

    readSelected: function(container){
      const items = [];
      container.querySelectorAll('[data-id]').forEach(el => {
        items.push({product_id: parseInt(el.getAttribute('data-id'), 10)});
      });
      return items;
    },

    searchProducts: function(q, resultsEl){
      const url = this.buildAdminUrl('catalog/product/autocomplete', { filter_name: q });
      fetch(url, { headers: {'X-Requested-With': 'XMLHttpRequest'} })
        .then(r => r.json())
        .then(json => {
          resultsEl.innerHTML = '';
          resultsEl.classList.remove('hidden');
          (json || []).slice(0, 10).forEach(item => {
            const div = document.createElement('div');
            div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer';
            div.textContent = item.name;
            div.addEventListener('click', () => this.addSelected(item));
            resultsEl.appendChild(div);
          });
        })
        .catch(() => { resultsEl.classList.add('hidden'); });
    },

    addSelected: function(item){
      if(this.state.items.find(i => i.product_id === item.product_id)) return;
      if(this.state.items.length >= this.config.maxItems) { alert('Може да изберете максимум 4 продукта.'); return; }

      this.state.items.push({product_id: item.product_id});
      const container = document.getElementById('featured-selected');
      const div = document.createElement('div');
      div.className = 'flex items-center justify-between border rounded px-3 py-2';
      div.setAttribute('data-id', item.product_id);
      div.innerHTML = `<span>${item.name}</span><button class="text-red-600 remove-selected" data-id="${item.product_id}">Премахни</button>`;
      container.appendChild(div);
    },

    save: function(){
      if(this.state.items.length !== this.config.maxItems){ alert('Трябва да изберете точно 4 продукта.'); return; }
      const body = new URLSearchParams();
      this.state.items.forEach(i => body.append('selected_products[]', i.product_id));

      fetch(window.ProductFeaturedConfig.submitUrl, {
        method: 'POST',
        headers: { 'X-Requested-With': 'XMLHttpRequest', 'Content-Type': 'application/x-www-form-urlencoded' },
        body: body.toString()
      })
      .then(r => r.json())
      .then(json => {
        if(json && json.success){ alert('Записът е успешен.'); }
        else { alert('Грешка: ' + (json.error || 'неизвестна')); }
      })
      .catch(() => alert('Възникна грешка при запис.'));
    }
  });

  document.addEventListener('DOMContentLoaded', function(){
    if(window.BackendModule){ ProductFeaturedModule.init(); }
  });
})();

