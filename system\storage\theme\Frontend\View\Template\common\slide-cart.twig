<!-- Slide Cart -->
<div class="cart-overlay"></div>
<div class="slide-cart flex flex-col">
    <div class="flex items-center justify-between p-4 border-b">
        <h2 class="text-lg font-semibold">Количка ({{ cart_count }})</h2>
        <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700" id="closeCart">
            <i class="ri-close-line ri-lg"></i>
        </button>
    </div>
    <div class="flex-1 overflow-y-auto p-4 space-y-4">
        <!-- Cart Items -->
        {% if cart_items is defined and cart_items|length > 0 %}
            {% for item in cart_items %}
                <div class="flex gap-3 pb-4 border-b">
                    <div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden">
                        <img src="{{ item.image }}" alt="{{ item.name }}" class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h3 class="font-medium text-sm">{{ item.name }}</h3>
                        {% if item.size %}
                            <div class="text-xs text-gray-500 mt-1">{{ item.size }}</div>
                        {% endif %}
                        <div class="flex justify-between items-end mt-2">
                            <div class="flex items-center gap-2">
                                <div class="font-semibold">{{' '}}{{ item.price }}</div>
                                <div class="text-sm text-gray-500 whitespace-nowrap">
                                    <span class="text-gray-400">x{{ item.quantity }}=</span>
                                    <span class="font-medium text-primary">{{' '}}{{ item.total }}</span>
                                </div>
                            </div>
                            <button class="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-500 transition-colors" onclick="removeFromCart('{{ item.cart_id }}')">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <p class="text-center text-gray-500">Няма добавени продукти в количката.</p>
        {% endif %}
    </div>
    <div class="p-4 bg-gray-50 space-y-4">
        {% if cart_items is defined and cart_items|length > 0 %}
            <div class="flex justify-between text-sm">
                <span class="text-gray-600">Общо:</span>
                <span class="font-semibold">{{' '}}{{ cart_total_formatted }}</span>
            </div>
            <div class="flex gap-2">
                <a href="{{ cart_url }}" class="flex-1 px-4 py-2 border border-primary text-primary rounded-button text-center hover:bg-primary hover:text-white transition-colors whitespace-nowrap">Виж количката</a>
                <a href="checkout" class="flex-1 bg-primary text-white text-center px-4 py-2 rounded-button hover:bg-opacity-90 whitespace-nowrap">Плащане</a>
            </div>
        {% else %}
            <div class="text-center text-gray-500">
                <p class="mb-4">Количката е празна</p>
                <a href="{{ home_url }}" class="inline-block px-4 py-2 bg-primary text-white rounded-button hover:bg-opacity-90">Продължи пазаруването</a>
            </div>
        {% endif %}
    </div>
</div>