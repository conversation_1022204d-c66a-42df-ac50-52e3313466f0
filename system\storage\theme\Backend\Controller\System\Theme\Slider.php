<?php

namespace Theme25\Backend\Controller\System\Theme;

/**
 * Суб-контролер за управление на началния слайдер
 *
 * @package Theme25\Backend\Controller\System\Theme
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Slider extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->loadThemeModel();
    }

    /**
     * Подготовка на данните за слайдера
     */
    public function prepareData() {
        $this->prepareSliderSettings()
             ->prepareSliderItems()
             ->prepareSliderLinks();
        
        return $this;
    }

    /**
     * Зареждане на модела за темата
     */
    private function loadThemeModel() {
        $this->loadModelAs('system/theme', 'themeModel');
        $this->loadModelAs('catalog/information', 'informationModel');
        $this->loadModelAs('catalog/category', 'categoryModel');
        $this->loadModelAs('catalog/product', 'productModel');
    }

    /**
     * Подготовка на настройките на слайдера
     */
    private function prepareSliderSettings() {
        $settings = $this->themeModel->getSliderSettings();

        $default_settings = [
            'slider_enabled' => 1,
            'auto_play' => 1,
            'interval' => 5,
            'animation' => 'fade',
            'show_arrows' => 1,
            'show_dots' => 1,
            'mobile_view' => 'show'
        ];

        $slider_settings = array_merge($default_settings, $settings);

        $this->setData('slider_settings', $slider_settings);

        return $this;
    }

    /**
     * Подготовка на слайдовете
     */
    private function prepareSliderItems() {
        $slides = $this->themeModel->getSliderItems();

        // Подготовка на данните за всеки слайд
        $prepared_slides = [];
        foreach ($slides as $slide) {
            $buttons = json_decode($slide['buttons'], true) ?: [];

            // Добавяне на имената на страниците към бутоните
            $buttons = $this->enrichButtonsWithPageNames($buttons);

            $prepared_slides[] = [
                'id' => $slide['id'],
                'title' => $slide['title'],
                'subtitle' => $slide['subtitle'],
                'image' => $slide['image'],
                'image_url' => $this->getImageUrl($slide['image']),
                'buttons' => $buttons,
                'sort_order' => $slide['sort_order'],
                'status' => $slide['status']
            ];
        }

        $this->setData('slider_items', $prepared_slides);

        return $this;
    }

    /**
     * Подготовка на линковете за слайдера
     */
    private function prepareSliderLinks() {
        $links = [
            'save_settings' => $this->getAdminLink('system/theme/save', 'tab=slider'),
            'add_slide' => $this->getAdminLink('system/theme/save', 'tab=slider&action=add'),
            'delete_slide' => $this->getAdminLink('system/theme/delete', 'tab=slider'),
            'reorder_slides' => $this->getAdminLink('system/theme/reorder', 'tab=slider')
        ];

        $this->setData('slider_links', $links);

        return $this;
    }

    /**
     * Запазване на настройките на слайдера
     */
    public function save($data) {
        $json = ['success' => false, 'message' => ''];
        
        try {
            $action = $data['action'] ?? 'settings';
            
            if ($action === 'settings') {
                $this->saveSliderSettings($data);
                $json = ['success' => true, 'message' => 'Настройките на слайдера са запазени успешно.'];
            } elseif ($action === 'add' || $action === 'edit') {
                $this->saveSliderItem($data);
                $json = ['success' => true, 'message' => 'Слайдът е запазен успешно.'];
            }
            
        } catch (Exception $e) {
            $json['message'] = 'Грешка при запазване: ' . $e->getMessage();
        }
        
        return $json;
    }

    /**
     * Запазване на настройките на слайдера
     */
    private function saveSliderSettings($data) {
        $settings = [
            'slider_enabled' => isset($data['slider_enabled']) ? 1 : 0,
            'auto_play' => isset($data['auto_play']) ? 1 : 0,
            'interval' => (int)($data['interval'] ?? 5),
            'animation' => $data['animation'] ?? 'fade',
            'show_arrows' => isset($data['show_arrows']) ? 1 : 0,
            'show_dots' => isset($data['show_dots']) ? 1 : 0,
            'mobile_view' => $data['mobile_view'] ?? 'show'
        ];
        $this->themeModel->saveSliderSettings($settings);
    }

    /**
     * Запазване на слайд
     */
    private function saveSliderItem($data) {
        // Обработка на buttons данните
        $buttons = $data['buttons'] ?? '[]';
        if (is_string($buttons)) {
            // Ако е вече JSON string, използваме го директно
            $buttons_json = $buttons;
        } else {
            // Ако е масив, кодираме го
            $buttons_json = json_encode($buttons);
        }

        $slide_data = [
            'title' => $data['title'] ?? '',
            'subtitle' => $data['subtitle'] ?? '',
            'image' => $data['image'] ?? '',
            'buttons' => $buttons_json,
            'status' => isset($data['status']) ? 1 : 0
        ];

        if (isset($data['slide_id']) && $data['slide_id'] > 0) {
            $this->themeModel->updateSliderItem($data['slide_id'], $slide_data);
        } else {
            $this->themeModel->addSliderItem($slide_data);
        }
    }

    /**
     * Изтриване на слайд
     */
    public function delete($id) {
        $json = ['success' => false, 'message' => ''];

        try {
            $this->themeModel->deleteSliderItem($id);
            $json = ['success' => true, 'message' => 'Слайдът е изтрит успешно.'];
        } catch (Exception $e) {
            $json['message'] = 'Грешка при изтриване: ' . $e->getMessage();
        }

        return $json;
    }

    /**
     * Пренареждане на слайдовете
     */
    public function reorder($order) {
        $json = ['success' => false, 'message' => ''];

        try {
            $this->themeModel->reorderSliderItems($order);
            $json = ['success' => true, 'message' => 'Редът на слайдовете е променен успешно.'];
        } catch (Exception $e) {
            $json['message'] = 'Грешка при пренареждане: ' . $e->getMessage();
        }

        return $json;
    }

    /**
     * Генериране на URL за изображение
     */
    private function getImageUrl($image) {
        if (empty($image)) {
            return '';
        }

        // Използваме \Theme25\Data за получаване на URL-а на изображението
        return \Theme25\Data::getInstance()->getImageWebUrl() . $image;
    }

    /**
     * Обогатяване на бутоните с имената на страниците, категориите и продуктите
     */
    private function enrichButtonsWithPageNames($buttons) {
        if (empty($buttons) || !is_array($buttons)) {
            return $buttons;
        }

        foreach ($buttons as &$button) {
            if (!isset($button['action']) || !isset($button['value']) ||
                !is_numeric($button['value']) || $button['value'] <= 0) {
                continue;
            }

            $item_id = (int)$button['value'];
            $action = $button['action'];

            switch ($action) {
                case 'page':
                    // Получаване на информацията за страницата
                    $page_info = $this->informationModel->getInformation($item_id);
                    if ($page_info && isset($page_info['title'])) {
                        $button['page_name'] = $page_info['title'];
                        $button['item_type'] = 'page';
                    } else {
                        $button['page_name'] = 'Страница не е намерена (ID: ' . $item_id . ')';
                        $button['item_type'] = 'page';
                    }
                    break;

                case 'category':
                    // Получаване на информацията за категорията
                    $category_info = $this->categoryModel->getCategory($item_id);
                    if ($category_info && isset($category_info['name'])) {
                        $button['page_name'] = strip_tags(html_entity_decode($category_info['name'], ENT_QUOTES, 'UTF-8'));
                        $button['item_type'] = 'category';
                    } else {
                        $button['page_name'] = 'Категория не е намерена (ID: ' . $item_id . ')';
                        $button['item_type'] = 'category';
                    }
                    break;

                case 'product':
                    // Получаване на информацията за продукта
                    $product_info = $this->productModel->getProduct($item_id);
                    if ($product_info && isset($product_info['name'])) {
                        $button['page_name'] = $product_info['name'];
                        $button['item_type'] = 'product';
                    } else {
                        $button['page_name'] = 'Продукт не е намерен (ID: ' . $item_id . ')';
                        $button['item_type'] = 'product';
                    }
                    break;
            }
        }

        return $buttons;
    }
}
