{% set selected = selected_products|default([]) %}
<div class="p-6">
  <div class="flex items-center justify-between mb-6">
    <h1 class="text-2xl font-semibold">Предложени продукти</h1>
    <a href="{{ back_url }}" class="text-primary hover:underline">Назад</a>
  </div>

  <div class="bg-white rounded shadow p-4">
    <p class="mb-4 text-gray-700">Изберете точно 4 продукта за показване на началната страница.</p>

    <div class="mb-4">
      <label class="block text-sm font-medium mb-1">Търсене на продукт</label>
      <input type="text" id="featured-search" class="border rounded px-3 py-2 w-full" placeholder="Въведете име на продукт...">
      <div id="featured-search-results" class="mt-2 border rounded hidden"></div>
    </div>

    <div>
      <label class="block text-sm font-medium mb-1">Избрани продукти (4)</label>
      <div id="featured-selected" class="grid grid-cols-1 md:grid-cols-2 gap-2">
        {% for p in selected %}
          <div class="flex items-center justify-between border rounded px-3 py-2 bg-white" data-id="{{ p.product_id }}" draggable="true">
            <div class="flex items-center gap-2">
              <span class="drag-handle cursor-move select-none text-gray-400" title="Премести">⋮⋮</span>
              <span>{{ p.name }}</span>
            </div>
            <button class="text-red-600 remove-selected" data-id="{{ p.product_id }}">Премахни</button>
          </div>
        {% endfor %}
      </div>
      <p class="text-xs text-gray-500 mt-2">Задръжте върху дръжката ⋮⋮ и влачете, за да пренаредите.</p>
    </div>

    <div class="mt-6">
      <button id="featured-save" class="bg-primary text-white px-6 py-2 rounded">Запази</button>
    </div>
  </div>
</div>

<script>
  window.ProductFeaturedConfig = {
    submitUrl: '{{ submit_url }}',
    userToken: '{{ user_token|default('') }}'
  };
</script>

