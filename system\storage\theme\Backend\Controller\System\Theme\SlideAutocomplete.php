<?php

namespace Theme25\Backend\Controller\System\Theme;

/**
 * Суб-контролер за автодопълване на слайдове
 *
 * @package Theme25\Backend\Controller\System\Theme
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class SlideAutocomplete extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->loadThemeModel();
    }

    /**
     * Зареждане на модела за темата
     */
    private function loadThemeModel() {
        $this->loadModelAs('system/theme', 'themeModel');
        $this->loadModelAs('catalog/information', 'informationModel');
        $this->loadModelAs('catalog/category', 'categoryModel');
        $this->loadModelAs('catalog/product', 'productModel');
    }

    /**
     * Автодопълване за слайдове
     */
    public function autocomplete($params) {
        $json = [];

        if (isset($params['id']) && $params['id'] > 0) {
            // Получаване на данни за конкретен слайд
            $slide = $this->themeModel->getSliderItem($params['id']);

            if ($slide) {
                $buttons = json_decode($slide['buttons'], true) ?: [];

                // Добавяне на имената на страниците, категориите и продуктите към бутоните
                $buttons = $this->enrichButtonsWithPageNames($buttons);
                $json = [
                    'id' => $slide['id'],
                    'title' => $slide['title'],
                    'subtitle' => $slide['subtitle'],
                    'image' => $slide['image'],
                    'image_url' => $this->getImageUrl($slide['image']),
                    'buttons' => $buttons,
                    'status' => (bool)$slide['status']
                ];
            }
        }

        return $json;
    }

    /**
     * Обогатяване на бутоните с имената на страниците, категориите и продуктите
     */
    private function enrichButtonsWithPageNames($buttons) {
        if (empty($buttons) || !is_array($buttons)) {
            return $buttons;
        }

        foreach ($buttons as &$button) {
            if (!isset($button['action']) || !isset($button['value']) ||
                !is_numeric($button['value']) || $button['value'] <= 0) {
                continue;
            }

            $item_id = (int)$button['value'];
            $action = $button['action'];

            switch ($action) {
                case 'page':
                    // Получаване на информацията за страницата
                    $page_info = $this->informationModel->getInformation($item_id);
                    if ($page_info && isset($page_info['title'])) {
                        $button['page_name'] = $page_info['title'];
                        $button['item_type'] = 'page';
                    } else {
                        $button['page_name'] = 'Страница не е намерена (ID: ' . $item_id . ')';
                        $button['item_type'] = 'page';
                    }
                    break;

                case 'category':
                    // Получаване на информацията за категорията
                    $category_info = $this->categoryModel->getCategory($item_id);
                    if ($category_info && isset($category_info['name'])) {
                        $button['page_name'] = strip_tags(html_entity_decode($category_info['name'], ENT_QUOTES, 'UTF-8'));
                        $button['item_type'] = 'category';
                    } else {
                        $button['page_name'] = 'Категория не е намерена (ID: ' . $item_id . ')';
                        $button['item_type'] = 'category';
                    }
                    break;

                case 'product':
                    // Получаване на информацията за продукта
                    $product_info = $this->productModel->getProduct($item_id);
                    if ($product_info && isset($product_info['name'])) {
                        $button['page_name'] = $product_info['name'];
                        $button['item_type'] = 'product';
                    } else {
                        $button['page_name'] = 'Продукт не е намерен (ID: ' . $item_id . ')';
                        $button['item_type'] = 'product';
                    }
                    break;
            }
        }

        return $buttons;
    }

    /**
     * Генериране на URL за изображение
     */
    private function getImageUrl($image) {
        if (empty($image)) {
            return '';
        }
        
        // Използваме \Theme25\Data за получаване на URL-а на изображението
        return \Theme25\Data::getInstance()->getImageWebUrl() . $image;
    }
}
