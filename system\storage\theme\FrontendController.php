<?php

namespace Theme25;

class FrontendController extends \Theme25\Controller
{

    public function __construct($registry, $route = '')
    {
        parent::__construct($registry, $route);

        if(!defined('ACCESS_DENIED')) {
            // Автоматична проверка за ограничение на достъпа до Frontend частта
            $this->checkFrontendAccessRestriction();
        }
    }

    public function addFrontendStyleWithVersion($styleNames)
    {
        if (!is_array($styleNames)) {
            $styleNames = [$styleNames];
        }

        $base = $this->getServer('HTTPS') ? HTTPS_SERVER : HTTP_SERVER;

        foreach ($styleNames as $styleName) {
            $styleUrl = $base . 'frontend_css/' . $styleName;
            $stylePath = DIR_THEME . 'Frontend/View/Css/' . $styleName;
            if (file_exists($stylePath)) {
                $lastModified = filemtime($stylePath);
                $styleUrl .= '?v=' . $lastModified;
                $this->document->addStyle($styleUrl);
            }
        }
    }

    public function addFrontendScriptWithVersion($scriptNames, $position = 'footer')
    {

        if (!is_array($scriptNames)) {
            $scriptNames = [$scriptNames];
        }
        $base = $this->getServer('HTTPS') ? HTTPS_SERVER : HTTP_SERVER;
        foreach ($scriptNames as $scriptName) {
            $scriptUrl = $base . 'frontend_js/' . $scriptName;
            $scriptPath = DIR_THEME . 'Frontend/View/Javascript/' . $scriptName;
            if (file_exists($scriptPath)) {
                $lastModified = filemtime($scriptPath);
                $scriptUrl .= '?v=' . $lastModified;
                $this->document->addScript($scriptUrl, $position);
            }
        }
    }

    /**
     * Показва страница за отказан достъп до Frontend частта
     */
    private function showAccessDeniedPage()
    {
        define('ACCESS_DENIED', true);
        http_response_code(503);
        // Задаваме заглавие на страницата
        $this->setTitle('Страницата се разработва');

        // // Получаваме логото на сайта от настройките
        $siteLogo = $this->getSiteLogo();

        $settings = $this->basicSettings->getSettings();
        $siteName = $settings['store_name'];
        $siteEmail = $settings['store_email'];
        $sitePhone = $settings['store_phone'];

        // Подготвяме данни за шаблона
        $this->setData([
            'heading_title' => 'Страницата се разработва',
            'text_message' => 'В момента извършваме подобрения на сайта. Скоро ще бъдем отново онлайн.',
            'site_logo' => $siteLogo,
            'site_name' => $siteName,
            'site_email' => $siteEmail,
            'site_phone' => $sitePhone,
        ]);

        // Рендираме шаблона за отказан достъп
        $output = $this->renderPartTemplate('error/access_denied');
        $this->setResponseOutput($output);
        $this->response->output();
        exit;
    }

    public function setUnderConstructionWhenIsNotDeveloper() {
        if(!isDeveloper()) {
			$this->data['under_construction'] = true;
		}
    }

    /**
     * Получава логото на сайта от настройките
     *
     * @return string URL към логото или празен string
     */
    public function getSiteLogo()
    {
        return ThemeData()->getImageServerUrl() . ltrim($this->basicSettings->getSettings()['store_logo'], '/');   
    }

    
    public function getSiteFavicon() {
        $settings = $this->basicSettings->getSettings();

        F()->log->developer($settings, __FILE__, __LINE__);

        $store_favicon_url = ThemeData()->getImageServerUrl() . $settings['store_favicon'];
        return $store_favicon_url;
    }

    public function getSiteName() {
        $settings = $this->basicSettings->getSettings();
        return $settings['store_name'];
    }

    
    /**
     * Проверява дали трябва да се приложи ограничение на достъпа до Frontend частта
     * Извиква се автоматично в конструктора на всеки контролер
     */
    private function checkFrontendAccessRestriction()
    {       
        // Определяме дали контролерът е Frontend чрез namespace анализ
        if (!$this->isFrontendController()) {
            return; // Не е Frontend контролер, няма нужда от проверка
        }

        // Проверяваме дали ограничението е активирано в настройките
        if (!$this->isFrontendAccessRestrictionEnabled()) {
            return; // Ограничението не е активирано
        }

        // Прилагаме проверката за административен достъп
        $this->checkAdminAccess();
    }

    /**
     * Проверява дали потребителят има активна административна сесия
     * Използва се за временно ограничение на достъпа до Frontend частта
     *
     * @param bool $showAccessDeniedPage Дали да покаже страница за отказан достъп (по подразбиране true)
     * @return bool True ако има административен достъп, false в противен случай
     */
    public function checkAdminAccess($showAccessDeniedPage = true)
    {
        // Проверяваме дали потребителят е логнат като администратор
        // Използваме безопасен метод, който работи и във Frontend и в Backend
        if (!$this->isAdminLoggedSafely()) {
            if ($showAccessDeniedPage) {
                $this->showAccessDeniedPage();
                exit;
            }
            return false;
        }

        // Проверяваме дали има валиден user_token в сесията
        $sessionToken = $this->getSession('user_token');
        if (empty($sessionToken)) {
            if ($showAccessDeniedPage) {
                $this->showAccessDeniedPage();
                exit;
            }
            return false;
        }
        return true;
    }


    /**
     * Проверява дали ограничението на достъпа до Frontend частта е активирано
     * в настройките от базата данни
     *
     * @return bool True ако ограничението е активирано
     */
    private function isFrontendAccessRestrictionEnabled()
    {
        try {
            // Използваме съществуващия метод за получаване на настройки от security модула

            $this->loadModelAs('setting/security', 'securitySettings');

            $setting = $this->securitySettings->getSecuritySetting('frontend_access_restriction', 0);

            return (bool)$setting;
        } catch (Exception $e) {
            // При грешка връщаме false (ограничението не е активно)
            return false;
        }
    }

    
    /**
     * Директна проверка на административна сесия чрез сесийните данни
     * Използва се във Frontend контролери където $this->user не е наличен
     *
     * @return bool True ако има валидна административна сесия
     */
    private function checkAdminSessionDirectly()
    {
        try {
            // Проверяваме дали има user_token в сесията
            $userToken = $this->getSession('user_token');
            if (empty($userToken)) {
                return false;
            }

            // Проверяваме дали има user_id в сесията (показва че администратор е логнат)
            $userId = $this->getSession('user_id');

            if (empty($userId)) {
                return false;
            }

            // Проверяваме дали сесията не е изтекла (ако има last_activity)
            $lastActivity = $this->getSession('last_activity');
            if ($lastActivity) {
                $sessionTimeout = 3600; // 1 час по подразбиране
                if ((time() - $lastActivity) > $sessionTimeout) {
                    return false;
                }
            }
            
            return true;

        } catch (Exception $e) {
            return false;
        }
    }


    /**
     * Безопасна проверка дали потребителят е логнат като администратор
     * Работи както във Frontend, така и в Backend контролери
     *
     * @return bool True ако е логнат като администратор
     */
    private function isAdminLoggedSafely()
    {
        try {
            // Първо проверяваме дали $this->user обектът съществува (Backend контролери)
            if (isset($this->user) && is_object($this->user) && method_exists($this->user, 'isLogged')) {
                return $this->user->isLogged();
            }

            // Алтернативна проверка чрез директен достъп до сесийните данни (Frontend контролери)
            return $this->checkAdminSessionDirectly();

        } catch (Exception $e) {
            F()->log('>>> isAdminLoggedSafely - грешка: ' . $e->getMessage(), __FILE__, __LINE__);
            return false;
        }
    }

   
}