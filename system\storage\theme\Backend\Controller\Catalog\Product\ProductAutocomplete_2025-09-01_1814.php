<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class ProductAutocomplete extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }
    
    /**
     * Обработва заявките за автодопълване на продукти
     * 
     * @param array $get GET параметри от заявката
     * @return array
     */
    public function autocomplete($get) {
        $json = [];
        
        $this->loadModelAs('catalog/product', 'productModel');
        
        $filter_data = [
            'start' => 0,
            'limit' => 10
        ];

        if (isset($get['filter_name'])) {
            $filter_data['filter_name'] = $get['filter_name'];
        }

        // Изключваме текущия продукт от резултатите, ако е зададен
        if (isset($get['exclude_product_id']) && !empty($get['exclude_product_id'])) {
            $filter_data['exclude_product_id'] = (int)$get['exclude_product_id'];
        }
        
        $results = $this->productModel->getProducts($filter_data);

        foreach ($results as $result) {
            // Подготвяне на изображението
            $thumb = '';
            if (!empty($result['image'])) {
                $this->loadModelAs('tool/Imageservice', 'imageService');
                $image_details = $this->imageService->getImageDetailsByPath($result['image'], 48, 48);
                $thumb = $image_details['resized_image_url'];
            } else {
                $this->loadModelAs('tool/image', 'imageModel');
                $thumb = $this->imageModel->resize('no_image.png', 48, 48);
            }

            $json[] = [
                'product_id' => $result['product_id'],
                'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8')),
                'model' => $result['model'],
                'price' => $result['price'],
                'thumb' => $thumb
            ];
        }
        
        return $json;
    }
}
