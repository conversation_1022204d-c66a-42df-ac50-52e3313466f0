<?php

// namespace Theme25;

// Дефиниране на DIR_THEME ако не е дефинирана
if (!defined('DIR_THEME')) define('DIR_THEME', __DIR__ . '/');
if (!defined('DIR_THEME_CACHE')) define('DIR_THEME_CACHE', DIR_THEME . 'Cache/');


// Дефиниране на константите
define('THEME_REQUEST_PROCESSOR', 'Theme25\RequestProcessor');
define('THEME_MODEL_PROCESSOR', 'Theme25\ModelProcessor');
define('THEME_VIEW_PROCESSOR', 'Theme25\ViewProcessor');
define('THEME_LIBRARY_PROCESSOR', 'Theme25\LibraryProcessor');
define('THEME_HELPER_PROCESSOR', 'Theme25\HelperProcessor');
define('THEME_CONFIG_PROCESSOR', 'Theme25\ConfigProcessor');
define('THEME_CALLBACK_PROCESSOR', 'Theme25\CallbackProcessor');


/**
 * Class ThemeAutoloader
 * Управлява автозареждането на класове за темата с поддръжка на кеширане.
 */
final class ThemeAutoloader
{
    /** @var self|null Инстанция на класа (Singleton) */
    private static ?self $instance = null;

    /** @var array<string, string> Карта на класовете и техните пътища (in-memory cache) */
    private array $classMap = [];

    /** @var string Път до кеш файла */
    private string $cacheFile;

    /** @var bool Флаг, който показва дали кешът е променен */
    private bool $isCacheDirty = false;

    /**
     * Конструкторът е private, за да се имплементира Singleton pattern.
     */
    private function __construct()
    {
        // Дефинираме пътя до кеш файла в директория, която е writable.
        // Може да се наложи да промените тази директория.
        $this->cacheFile = DIR_THEME_CACHE . 'autoloader_map.php.cache';

        $this->loadCache();

        // Регистрираме основния метод за зареждане на класове.
        spl_autoload_register([$this, 'loadClass']);

        // Регистрираме функция, която ще се изпълни при край на скрипта, за да запази кеша.
        // register_shutdown_function([$this, 'saveCache']);
        register_shutdown_function([$this, 'processShutdownTasks']);
    }

    /**
     * Взима или създава единствената инстанция на Autoloader-а.
     */
    public static function register()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
    }

    private function loadCache()
    {
        if (!is_readable($this->cacheFile)) {
            return;
        }

        $content = @file_get_contents($this->cacheFile);
        if ($content === false || $content === '') {
            return;
        }

        // Безопасно unserialize (избягваме Notice/Warning при увреден файл)
        $map = @unserialize($content, ['allowed_classes' => false]);
        if (is_array($map)) {
            $this->classMap = $map;
        }
    }


    /**
     * Методът, който се регистрира в SPL Autoloader опашката.
     * @param string $class Името на класа за зареждане.
     */
    public function loadClass(string $class)
    {
        // 1. Проверяваме в кеша в паметта.
        if (isset($this->classMap[$class])) {
            $cached_path = $this->classMap[$class];

            // Проверка дали кешираният път все още е валиден.
            if ($cached_path && file_exists($cached_path)) {
                // Ако кешът е валиден, зареждаме файла и приключваме.
                include_once $cached_path;
                return;
            }
            // Ако кешът е НЕВАЛИДЕН (файлът е изтрит/преместен),
            // просто оставяме кода да продължи надолу, за да извърши ново търсене.
        }

        // 2. Ако класът не е в кеша или кешът е невалиден, търсим файла.
        $namespace = 'Theme25\\';
        if (strpos($class, $namespace) !== 0) {
            return;
        }
        
        $file_path = str_replace($namespace, '', $class);
        $file_path = ltrim($file_path, '\\');
        $file_path = DIR_THEME . str_replace('\\', '/', $file_path) . '.php';
        
        $found_file = $this->findFileCaseInsensitive($file_path);

        // 3. Обновяваме кеша в паметта с новия резултат (валиден път или `false`).
        // Това автоматично ще коригира невалидния запис.
        $this->classMap[$class] = $found_file;
        $this->isCacheDirty = true; // Маркираме, че кешът трябва да се запише във файла.

        if ($found_file) {
            include_once $found_file;
        } elseif (strpos($class, 'Theme25\\Backend\\Helper\\') === 0 && function_exists('error_log')) {
            error_log("Theme25 Autoloader: Helper class file not found: '$file_path' for class '$class'");
        }
    }

    public function processShutdownTasks()
    {
        $this->saveCache();
        if(class_exists('ShutdownTaskManager')) {
            ShutdownTaskManager::executeTasks();
        }
    }


    /**
     * Запазва кеша от паметта във файл, ако е имало промени.
     */
    public function saveCache()
    {

        // Записваме само ако има промени и ако кешът не е празен.
        if (!$this->isCacheDirty || empty($this->classMap)) {
            return;
        }
        
        try {
            // Уверяваме се, че директорията съществува
            $cacheDir = dirname($this->cacheFile);
            if (!is_dir($cacheDir)) {
                // На Windows 0755 е ок; подаваме recursive=true
                if (!@mkdir($cacheDir, 0755, true) && !is_dir($cacheDir)) {
                    return; // Не прекъсваме заявката при неуспех
                }
            }

            // Използваме LOCK_EX за сигурен запис при конкурентен достъп.
            @file_put_contents($this->cacheFile, serialize($this->classMap), LOCK_EX);
        } catch (\Throwable $e) {
            if (function_exists('error_log')) {
                error_log('ThemeAutoloader saveCache error: ' . $e->getMessage());
            }
            // Никога не прекъсваме заявката заради кеша
        }
    }

    /**
     * Търси файл, без да се влияе от регистъра на името му.
     * @return string|false Пълният път или false.
     */
    private function findFileCaseInsensitive(string $path)
    {
        if (file_exists($path)) {
            return $path;
        }
        
        $directory = dirname($path);
        $filename = basename($path);

        if (!is_dir($directory)) {
            return false;
        }

        $files = scandir($directory);
        if ($files === false) {
            return false;
        }

        foreach ($files as $file) {
            if (strcasecmp($file, $filename) === 0) {
                return $directory . '/' . $file;
            }
        }

        return false;
    }

    /**
     * Публичен статичен метод за изчистване на кеша.
     */
    public static function clearCache()
    {
        $cacheFile = DIR_THEME_CACHE . 'autoloader_map.php.cache';
        if (file_exists($cacheFile)) {
            return @unlink($cacheFile);
        }
        return true;
    }
}

include_once DIR_THEME . 'ShutdownTaskManager.php';

// ----- ИНИЦИАЛИЗАЦИЯ -----
ThemeAutoloader::register();


// ----- Функцията за достъп до Data остава същата, но извън namespace-а -----
if (!function_exists('ThemeData')) {
    function ThemeData() {
        return \Theme25\Data::getInstance();
    }
}

// Зареждаме Data класа ръчно, тъй като той може да е нужен преди autoloader-a да го намери.
// Уверете се, че пътят е верен.
if (file_exists(DIR_THEME . 'Data.php')) {
    include_once DIR_THEME . 'Data.php';
}


/**
 * Глобална функция за лесен достъп до CommonMethods
 *
 * @param \Registry|null $registry Registry обект (задължителен при първо извикване)
 * @return CommonMethods
 */
function CM($registry = null) {
    if($registry) GlobalRegistry::$registry = $registry;
    return \Theme25\CommonMethods::getInstance($registry);
}

function Currency() {
    return new \Theme25\Currency(GlobalRegistry::$registry);
}

class GlobalRegistry {
    public static $registry;
}

class DataBases {
    public static $firstDb;
    public static $secondDb;
    public static $firstDbPrefix;
    public static $secondDbPrefix;

    public static function getFirstDb() {
        return self::$firstDb;
    }

    public static function setFirstDb($db) {
        self::$firstDb = $db;
    }

    public static function getSecondDb() {
        return self::$secondDb;
    }

    public static function setSecondDb($db) {
        self::$secondDb = $db;
    }

    public static function getFirstDbPrefix() {
        return self::$firstDbPrefix;
    }

    public static function setFirstDbPrefix($prefix) {
        self::$firstDbPrefix = $prefix;
    }

    public static function getSecondDbPrefix() {
        return self::$secondDbPrefix;
    }

    public static function setSecondDbPrefix($prefix) {
        self::$secondDbPrefix = $prefix;
    }
}



include_once DIR_THEME . 'NoHup.php';