(function(){
  'use strict';

  // Разширяваме BackendModule
  const ProductFeaturedModule = Object.create(BackendModule);

  Object.assign(ProductFeaturedModule, {
    config: {
      maxItems: 4,
      debounceMs: 300
    },

    init: function() {
      const search = document.getElementById('featured-search');
      const results = document.getElementById('featured-search-results');
      const selected = document.getElementById('featured-selected');
      const saveBtn = document.getElementById('featured-save');
      if(!search || !results || !selected || !saveBtn) return;

      this.state = {items: this.readSelected(selected)};

      // Състояние за търсене и странициране
      this.searchState = {
        query: '',
        start: 0,
        limit: 10,
        loading: false,
        hasMore: true,
        abortController: null,
        resultsEl: results
      };

      // Показвай първите 10 предложения при фокус/клик
      const showInitial = () => {
        if (search.value.trim().length === 0) {
          this.loadInitialProducts();
        } else {
          this.searchProducts(search.value.trim());
        }
      };
      search.addEventListener('focus', showInitial);
      search.addEventListener('click', showInitial);

      // Търсене с debounce при въвеждане
      let t;
      search.addEventListener('input', (e) => {
        clearTimeout(t);
        const q = e.target.value.trim();
        if(q.length === 0){
          this.loadInitialProducts();
          return;
        }
        t = setTimeout(() => this.searchProducts(q), this.config.debounceMs);
      });

      // Премахване на избран
      selected.addEventListener('click', (e) => {
        const removeBtn = e.target.closest('.remove-selected');
        if(!removeBtn) return;
        const id = parseInt(removeBtn.getAttribute('data-id'), 10);
        const container = document.getElementById('featured-selected');
        let card = null;
        if (container) { card = container.querySelector(`[data-id="${id}"]`); }
        if (!card) { card = removeBtn.closest('.featured-selected-item') || removeBtn.closest('[data-id]'); }
        if (!card) return;
        card.remove();
        this.updateStateFromDom();
      });

      // Затваряне на dropdown при избор/клик извън него
      document.addEventListener('click', (ev) => {
        const wrapper = document.getElementById('featured-search-wrapper');
        if(!wrapper) return;
        const within = wrapper.contains(ev.target);
        if(!within){ results.classList.add('hidden'); }
      });

      // Инициализиране на drag & drop
      this.attachDnd(selected);

      // Infinite scroll за резултатите
      this.attachInfiniteScroll(results);

      // Запис
      saveBtn.addEventListener('click', () => this.save());

      // Стартираме с правилен брояч
      this.updateSelectedCounter();
    },

    readSelected: function(container){
      const items = [];
      container.querySelectorAll('[data-id]').forEach(el => {
        items.push({product_id: parseInt(el.getAttribute('data-id'), 10)});
      });
      return items;
    },

    updateStateFromDom: function(){
      const container = document.getElementById('featured-selected');
      const items = [];
      container.querySelectorAll('[data-id]').forEach(el => {
        items.push({product_id: parseInt(el.getAttribute('data-id'), 10)});
      });
      this.state.items = items;
      this.updateSelectedCounter();
    },

    updateSelectedCounter: function(){
      const el = document.getElementById('featured-selected-count');
      if(el){ el.textContent = String(this.state.items.length); }
    },

    attachDnd: function(container){
      if(this._dndAttached) return;
      this._dndAttached = true;
      let draggingEl = null;

      container.addEventListener('dragstart', (e) => {
        const item = e.target.closest('[data-id]');
        if(!item) return e.preventDefault();
        // позволяваме drag само при хващане на handle
        if(!e.target.closest('.drag-handle')){ e.preventDefault(); return; }
        draggingEl = item;
        item.classList.add('opacity-50','ring-2','ring-primary');
        e.dataTransfer.effectAllowed = 'move';
      });

      container.addEventListener('dragover', (e) => {
        e.preventDefault();
        if(!draggingEl) return;
        const afterElement = this.getDragAfterElement(container, e.clientY, draggingEl);
        if(afterElement == null){
          container.appendChild(draggingEl);
        } else if(afterElement !== draggingEl) {
          container.insertBefore(draggingEl, afterElement);
        }
      });

      container.addEventListener('drop', (e) => {
        e.preventDefault();
        if(!draggingEl) return;
        draggingEl.classList.remove('opacity-50','ring-2','ring-primary');
        draggingEl = null;
        this.updateStateFromDom();
      });

      container.addEventListener('dragend', () => {
        if(draggingEl){ draggingEl.classList.remove('opacity-50','ring-2','ring-primary'); }
        draggingEl = null;
        this.updateStateFromDom();
      });
    },

    getDragAfterElement: function(container, y, draggingEl){
      const draggableElements = [...container.querySelectorAll('[data-id]')]
        .filter(el => el !== draggingEl);
      let closest = null;
      let closestOffset = Number.NEGATIVE_INFINITY;
      draggableElements.forEach(child => {
        const box = child.getBoundingClientRect();
        const offset = y - box.top - box.height / 2;
        if (offset < 0 && offset > closestOffset) {
          closestOffset = offset;
          closest = child;
        }
      });
      return closest;
    },

    buildAutocompleteUrl: function(params){
      // По модела от product-form.js и глобалното търсене използваме директен URL с user_token
      const userToken = BackendModule.config.userToken;
      const ts = Date.now();
      const type = 'product';
      const limit = params.limit || this.searchState.limit;
      let url = `index.php?route=catalog/product/autocomplete&type=${type}&limit=${limit}&user_token=${userToken}&_=${ts}`;
      if (params.filter_name && params.filter_name.length > 0) {
        url += `&filter_name=${encodeURIComponent(params.filter_name)}`;
      }
      if (typeof params.start === 'number') {
        url += `&start=${params.start}`;
      }
      return url;
    },

    renderResultItem: function(item){
      const div = document.createElement('div');
      div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center gap-3 border-b last:border-b-0';
      div.innerHTML = `
        <img src="${item.thumb || ''}" alt="" class="w-8 h-8 rounded object-cover"/>
        <div class="flex-1">
          <div class="font-medium">${item.name}</div>
          <div class="text-xs text-gray-500">Модел: ${item.model || 'N/A'} | Цена: ${item.price || '0.00'} лв.</div>
        </div>
      `;
      div.addEventListener('click', () => {
        this.addSelected(item);
        // Затваряме dropdown-а и изчистваме полето
        const results = document.getElementById('featured-search-results');
        if(results){ results.classList.add('hidden'); }
        const input = document.getElementById('featured-search');
        if(input){ input.value=''; }
      });
      return div;
    },

    resetSearch: function(){
      this.searchState.start = 0;
      this.searchState.hasMore = true;
      this.searchState.resultsEl.innerHTML = '';
      this.searchState.resultsEl.classList.remove('hidden');
    },

    loadInitialProducts: function(){
      if (this.searchState.loading) return;
      this.searchState.query = '';
      this.resetSearch();
      this.fetchAndRenderSuggestions('');
    },

    searchProducts: function(q){
      if (this.searchState.loading) return;
      this.searchState.query = q;
      this.resetSearch();
      this.fetchAndRenderSuggestions(q);
    },

    fetchAndRenderSuggestions: function(q){
      if (this.searchState.loading || !this.searchState.hasMore) return;
      this.searchState.loading = true;
      // Abort предишна заявка
      if (this.searchState.abortController) {
        try { this.searchState.abortController.abort(); } catch(e) {}
      }
      this.searchState.abortController = new AbortController();

      const url = this.buildAutocompleteUrl({ filter_name: q, start: this.searchState.start, limit: this.searchState.limit });
      fetch(url, { headers: { 'Cache-Control': 'no-cache' }, signal: this.searchState.abortController.signal })
        .then(r => {
          if (!r.ok) throw new Error('HTTP ' + r.status);
          return r.json();
        })
        .then(json => {
          const list = Array.isArray(json) ? json : [];
          if (this.searchState.start === 0) {
            this.searchState.resultsEl.innerHTML = '';
          }
          list.forEach(item => {
            this.searchState.resultsEl.appendChild(this.renderResultItem(item));
          });
          if (list.length < this.searchState.limit) {
            this.searchState.hasMore = false;
          } else {
            this.searchState.start += this.searchState.limit;
          }
          this.searchState.resultsEl.classList.remove('hidden');
        })
        .catch(() => {
          // скриваме при грешка
          this.searchState.resultsEl.classList.add('hidden');
        })
        .finally(() => {
          this.searchState.loading = false;
        });
    },

    attachInfiniteScroll: function(resultsEl){
      resultsEl.addEventListener('scroll', () => {
        const nearBottom = resultsEl.scrollTop + resultsEl.clientHeight >= resultsEl.scrollHeight - 10;
        if (nearBottom && this.searchState.hasMore && !this.searchState.loading) {
          this.fetchAndRenderSuggestions(this.searchState.query);
        }
      });
    },


    addSelected: function(item){
      // Предпазване от дубликати – проверка по DOM и по state
      const container = document.getElementById('featured-selected');
      if (container && container.querySelector(`[data-id="${item.product_id}"]`)) return;
      if(this.state.items.find(i => i.product_id === item.product_id)) return;
      if(this.state.items.length >= this.config.maxItems) { alert('Може да изберете максимум 4 продукта.'); return; }

      // Добавяме картата, после синхронизираме state от DOM (избягваме двойно броене)
      const div = document.createElement('div');
      div.className = 'featured-selected-item flex items-center justify-between rounded px-3 py-2 bg-gray-100 border border-gray-200';
      div.setAttribute('data-id', item.product_id);
      div.setAttribute('draggable', 'true');
      div.innerHTML = `
        <div class="flex items-center gap-3">
          <span class="drag-handle cursor-move select-none text-gray-400" title="Премести">⋮⋮</span>
          ${item.thumb ? `<img src="${item.thumb}" alt="" class="w-10 h-10 rounded object-cover"/>` : ''}
          <div class="leading-tight">
            <div class="font-medium text-gray-800">${item.name}</div>
            ${item.model ? `<div class="text-xs text-gray-500">Код: ${item.model}</div>` : ''}
          </div>
        </div>
        <button class="remove-selected w-8 h-8 flex items-center justify-center text-gray-500 hover:text-red-600" data-id="${item.product_id}" aria-label="Премахни"><span aria-hidden="true">✕</span></button>
      `;
      container.appendChild(div);
      this.updateStateFromDom();
    },

    save: function(){
      if(this.state.items.length !== this.config.maxItems){ alert('Трябва да изберете точно 4 продукта.'); return; }
      const body = new URLSearchParams();
      this.state.items.forEach((i, idx) => {
        body.append('selected_products[]', i.product_id);
        body.append('sort_order[]', idx);
      });

      fetch(window.ProductFeaturedConfig.submitUrl, {
        method: 'POST',
        headers: { 'X-Requested-With': 'XMLHttpRequest', 'Content-Type': 'application/x-www-form-urlencoded' },
        body: body.toString()
      })
      .then(r => r.json())
      .then(json => {
        if(json && json.success){ alert('Записът е успешен.'); }
        else { alert('Грешка: ' + (json.error || 'неизвестна')); }
      })
      .catch(() => alert('Възникна грешка при запис.'));
    }
  });

  document.addEventListener('DOMContentLoaded', function(){
    if(window.BackendModule){ ProductFeaturedModule.init(); }
  });
})();

