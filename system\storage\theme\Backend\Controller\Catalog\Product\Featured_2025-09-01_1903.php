<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Featured extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    public function execute() {
        $this->setTitle('Предложени продукти');
        $this->initAdminData();
        $this->addBackendScriptWithVersion('product-featured.js', 'footer');

        // Създаваме таблицата ако не съществува
        $this->createFeaturedTableIfMissing();

        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/product_featured');
    }

    private function prepareData() {
        $selected = $this->getCurrentFeaturedProducts();

        // Зареждаме имената/основните полета за визуализация
        $this->loadModelAs('catalog/product', 'productModel');

        $products = [];
        foreach ($selected as $row) {
            $p = $this->productModel->getProduct((int)$row['product_id']);
            if (!$p) continue;
            $products[] = [
                'product_id' => (int)$p['product_id'],
                'name' => $p['name'],
            ];
        }

        $this->setData([
            'selected_products' => $products,
            'submit_url' => $this->getAdminLink('catalog/product/featured/submit'),
            'back_url' => $this->getAdminLink('catalog/product/featured'),
        ]);
    }

    /**
     * AJAX: Запазва списък от точно 4 продукта като предложени
     */
    public function submit() {
        $json = ['success' => false];
        try {
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $post = $this->requestPost();
            $ids = isset($post['selected_products']) ? $post['selected_products'] : [];
            $orders = isset($post['sort_order']) ? $post['sort_order'] : [];
            if (!is_array($ids)) $ids = [];
            if (!is_array($orders)) $orders = [];

            // Премахване на дубликати и каст към int
            $ids = array_map('intval', $ids);
            $orders = array_map('intval', $orders);

            if (count($ids) !== 4) {
                throw new \Exception('Трябва да изберете точно 4 продукта.');
            }

            // Изграждаме масив от [product_id => sort_order] според позицията
            $pairs = [];
            foreach ($ids as $idx => $pid) {
                $pairs[(int)$pid] = isset($orders[$idx]) ? (int)$orders[$idx] : $idx;
            }

            // Валидация съществуване на продукти
            $this->loadModelAs('catalog/product', 'productModel');
            foreach ($pairs as $pid => $sort) {
                $p = $this->productModel->getProduct((int)$pid);
                if (!$p) {
                    throw new \Exception('Невалиден продукт ID: ' . (int)$pid);
                }
            }

            $this->dbQuery("TRUNCATE TABLE `" . DB_PREFIX . "product_featured`");
            foreach ($pairs as $pid => $sort) {
                $sql = "INSERT INTO `" . DB_PREFIX . "product_featured` SET product_id = '" . (int)$pid . "', sort_order='" . (int)$sort . "', date_added = NOW(), created_by='" . (int)$this->getUserId() . "'";
                $this->dbQuery($sql);
            }

            $json['success'] = true;
            $json['message'] = 'Предложените продукти са запазени успешно.';
        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    private function getCurrentFeaturedProducts() {
        $table = DB_PREFIX . 'product_featured';
        try {
            $exists = $this->dbQuery("SHOW TABLES LIKE '" . $this->dbEscape($table) . "'");
            if ($exists && $exists->num_rows > 0) {
                $res = $this->dbQuery("SELECT product_id, sort_order FROM `".$table."` ORDER BY sort_order ASC, id ASC");
                return $res->rows;
            }
        } catch (\Exception $e) {}
        return [];
    }

    private function createFeaturedTableIfMissing() {
        $sql = "CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "product_featured` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `product_id` INT(11) NOT NULL,
            `sort_order` INT(11) NOT NULL DEFAULT 0,
            `date_added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `created_by` INT(11) NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `uniq_product` (`product_id`),
            KEY `idx_sort` (`sort_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";
        try { $this->dbQuery($sql); } catch (\Exception $e) {}
    }
}

