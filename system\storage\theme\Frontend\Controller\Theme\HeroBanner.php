<?php

namespace Theme25\Frontend\Controller\Theme;

class HeroBanner extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'theme/hero_banner');
        $this->loadModelAs('system/theme', 'themeModel');
    }

    /**
     * Рендира динамичния Hero Banner като частичен шаблон
     * Връща HTML, готов за инжектиране в основния шаблон
     */
    public function index() {

        // Настройки на слайдера
        $settings = $this->themeModel->getSliderSettings();
        $default_settings = [
            'slider_enabled' => 1,
            'auto_play' => 1,
            'interval' => 5,
            'animation' => 'fade',
            'show_arrows' => 1,
            'show_dots' => 1,
            'mobile_view' => 'show'
        ];
        $slider_settings = array_merge($default_settings, is_array($settings) ? $settings : []);

        // Слайдове
        $items = $this->themeModel->getSliderItems();
        $slides = [];
        if (is_array($items)) {
            foreach ($items as $item) {
                if (!isset($item['status']) || (int)$item['status'] !== 1) {
                    continue; // само активни
                }

                $imageRel = isset($item['image']) ? trim($item['image']) : '';
                $imageUrl = $imageRel ? (function_exists('ThemeData') ? (ThemeData()->getImageServerUrl() . ltrim($imageRel, '/')) : $imageRel) : '';

                // Бутони
                $buttonsRaw = isset($item['buttons']) ? $item['buttons'] : '[]';
                $buttonsArr = [];
                if (is_string($buttonsRaw)) {
                    $decoded = json_decode($buttonsRaw, true);
                    $buttonsArr = is_array($decoded) ? $decoded : [];
                } elseif (is_array($buttonsRaw)) {
                    $buttonsArr = $buttonsRaw;
                }

                $preparedButtons = [];
                foreach ($buttonsArr as $btn) {
                    $action = isset($btn['action']) ? $btn['action'] : '';
                    $value = isset($btn['value']) ? $btn['value'] : '';
                    $text  = isset($btn['text']) ? $btn['text'] : (isset($btn['label']) ? $btn['label'] : '');
                    $style = isset($btn['style']) ? $btn['style'] : '';

                    $href = '#';
                    $onclick = '';

                    if ($action === 'url' && $value) {
                        $href = $value;
                    } elseif ($action === 'page' && is_numeric($value) && (int)$value > 0) {
                        $href = $this->getLink('information/information', 'information_id=' . (int)$value, true);
                    } elseif ($action === 'category' && is_numeric($value) && (int)$value > 0) {
                        $href = $this->getLink('product/category', 'path=' . (int)$value, true);
                    } elseif ($action === 'product' && is_numeric($value) && (int)$value > 0) {
                        $href = $this->getLink('product/product', 'product_id=' . (int)$value, true);
                    } elseif ($action === 'javascript' && $value) {
                        // За JavaScript код - задаваме onclick атрибут
                        $href = '#';
                        $onclick = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                    }

                    $preparedButtons[] = [
                        'text' => $text,
                        'href' => $href,
                        'style' => $style,
                        'action' => $action,
                        'onclick' => $onclick
                    ];
                }

                $slides[] = [
                    'id' => isset($item['id']) ? (int)$item['id'] : 0,
                    'title' => isset($item['title']) ? $item['title'] : '',
                    'subtitle' => isset($item['subtitle']) ? $item['subtitle'] : '',
                    'image_url' => $imageUrl,
                    'buttons' => $preparedButtons,
                    'sort_order' => isset($item['sort_order']) ? (int)$item['sort_order'] : 0
                ];
            }
        }

        // Ако е изключен или няма слайдове – върни празен стринг
        if ((int)$slider_settings['slider_enabled'] !== 1 || empty($slides)) {
            return '';
        }

        $heroBannerSettings = [
            'loop' => true
        ];

        if (!empty($slider_settings['auto_play'])) {
            $heroBannerSettings['autoplay'] = [
                'delay' => $slider_settings['interval'] * 1000,
                'disableOnInteraction' => false,
            ];
        }

        if (!empty($slider_settings['animation']) && $slider_settings['animation'] === 'fade') {
            $heroBannerSettings['effect'] = 'fade';
            $heroBannerSettings['fadeEffect'] = [
                'crossFade' => true,
            ];
        } elseif (!empty($slider_settings['animation'])) {
            $heroBannerSettings['effect'] = $slider_settings['animation'];
        }

        if (!empty($slider_settings['show_arrows'])) {
            $heroBannerSettings['navigation'] = [
                'nextEl' => '.swiper-button-next',
                'prevEl' => '.swiper-button-prev',
            ];
        }

        if (!empty($slider_settings['show_dots'])) {
            $heroBannerSettings['pagination'] = [
                'el' => '.swiper-pagination',
                'clickable' => true,
            ];
        }


        $this->setData([
            'slides' => $slides,
            'slider_settings' => $slider_settings,
            'heroBannerSettings' => $heroBannerSettings
        ]);

        // Рендер на частичния шаблон
        $html = $this->renderPartTemplate('theme/hero_banner', $this->getData());
        return $html;
    }
}
