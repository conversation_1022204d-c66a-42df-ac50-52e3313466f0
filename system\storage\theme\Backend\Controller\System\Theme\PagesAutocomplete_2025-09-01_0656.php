<?php

namespace Theme25\Backend\Controller\System\Theme;

/**
 * Суб-контролер за автодопълване на страници
 *
 * @package Theme25\Backend\Controller\System\Theme
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class PagesAutocomplete extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->loadModels();
    }

    /**
     * Зареждане на необходимите модели
     */
    private function loadModels() {
        $this->loadModelAs('catalog/information', 'informationModel');
        $this->loadModelAs('catalog/category', 'categoryModel');
        $this->loadModelAs('catalog/product', 'productModel');
    }

    /**
     * Автодопълване за страници, категории и продукти
     */
    public function autocomplete($params) {



        $json = [];
        $searchTerm = isset($params['filter_name']) ? trim($params['filter_name']) : '';
        if(!isset($params['filter_name']) && isset($params['q'])) {
            $searchTerm = trim($params['q']);
        }
        $limit = isset($params['limit']) ? (int)$params['limit'] : 25;

        // Ако няма търсен термин, връщаме само информационните страници
        if (empty($searchTerm)) {
            return $this->getInformationPages($limit);
        }

        // Търсене в информационни страници
        $informationResults = $this->searchInformationPages($searchTerm, 10);

        // Търсене в категории
        $categoryResults = $this->searchCategories($searchTerm, 10);

        // Търсене в продукти
        $productResults = $this->searchProducts($searchTerm, 10);

        // Комбиниране на резултатите
        $allResults = array_merge($informationResults, $categoryResults, $productResults);

        // Сортиране по релевантност
        $sortedResults = $this->sortByRelevance($allResults, $searchTerm);

        // Ограничаване на резултатите
        return array_slice($sortedResults, 0, $limit);
    }

    /**
     * Получаване на информационни страници без търсене
     */
    private function getInformationPages($limit = 25) {
        $json = [];

        $filter_data = [
            'filter_status' => 1,
            'start' => 0,
            'limit' => $limit
        ];

        $pages = $this->informationModel->getInformations($filter_data);

        foreach ($pages as $page) {
            $json[] = [
                'name' => '(Страница) ' . $page['title'],
                'value' => $this->getPageUrl($page['information_id']),
                'type' => 'page'
            ];
        }

        return $json;
    }

    /**
     * Търсене в информационни страници
     */
    private function searchInformationPages($searchTerm, $limit = 10) {
        $json = [];

        $results = $this->informationModel->searchInformations($searchTerm, $limit);

        foreach ($results as $page) {
            $json[] = [
                'name' => '(Страница) ' . $page['name'],
                'value' => $this->getPageUrl($page['id']),
                'type' => 'page',
                'relevance' => $this->calculateRelevance($page['name'], $searchTerm)
            ];
        }

        return $json;
    }

    /**
     * Търсене в категории
     */
    private function searchCategories($searchTerm, $limit = 10) {
        $json = [];

        $results = $this->categoryModel->searchCategories($searchTerm, $limit);

        foreach ($results as $category) {
            $json[] = [
                'name' => '(Категория) ' . strip_tags(html_entity_decode($category['name'], ENT_QUOTES, 'UTF-8')),
                'value' => $this->getCategoryUrl($category['id']),
                'type' => 'category',
                'relevance' => $this->calculateRelevance($category['name'], $searchTerm)
            ];
        }

        return $json;
    }

    /**
     * Търсене в продукти
     */
    private function searchProducts($searchTerm, $limit = 10) {
        $json = [];

        $results = $this->productModel->searchProducts($searchTerm, $limit);

        foreach ($results as $product) {
            $json[] = [
                'name' => '(Продукт) ' . $product['name'],
                'value' => $this->getProductUrl($product['id']),
                'type' => 'product',
                'relevance' => $this->calculateRelevance($product['name'], $searchTerm)
            ];
        }

        return $json;
    }

    /**
     * Генериране на URL за страница
     */
    private function getPageUrl($information_id) {
        return 'index.php?route=information/information&information_id=' . $information_id;
    }

    /**
     * Генериране на URL за категория
     */
    private function getCategoryUrl($category_id) {
        return 'index.php?route=catalog/category&category_id=' . $category_id;
    }

    /**
     * Генериране на URL за продукт
     */
    private function getProductUrl($product_id) {
        return 'index.php?route=catalog/product&product_id=' . $product_id;
    }

    /**
     * Изчисляване на релевантност на резултата
     */
    private function calculateRelevance($text, $searchTerm) {
        $text = mb_strtolower($text);
        $searchTerm = mb_strtolower($searchTerm);

        // Точно съвпадение - най-висока релевантност
        if ($text === $searchTerm) {
            return 100;
        }

        // Започва с търсения термин - висока релевантност
        if (mb_strpos($text, $searchTerm) === 0) {
            return 90;
        }

        // Съдържа търсения термин - средна релевантност
        if (mb_strpos($text, $searchTerm) !== false) {
            return 70;
        }

        // Съдържа думи от търсения термин - ниска релевантност
        $searchWords = explode(' ', $searchTerm);
        $matchedWords = 0;

        foreach ($searchWords as $word) {
            if (mb_strpos($text, $word) !== false) {
                $matchedWords++;
            }
        }

        if ($matchedWords > 0) {
            return 50 + ($matchedWords / count($searchWords)) * 20;
        }

        return 0;
    }

    /**
     * Сортиране на резултатите по релевантност
     */
    private function sortByRelevance($results, $searchTerm) {
        usort($results, function($a, $b) {
            // Първо сортираме по релевантност (по-висока релевантност отгоре)
            // Релевантността има най-висок приоритет
            if ($a['relevance'] !== $b['relevance']) {
                return $b['relevance'] - $a['relevance'];
            }

            // При еднаква релевантност, сортираме по тип (страници, категории, продукти)
            // Но само ако релевантността е еднаква
            $typeOrder = ['page' => 1, 'category' => 2, 'product' => 3];
            $aOrder = isset($typeOrder[$a['type']]) ? $typeOrder[$a['type']] : 4;
            $bOrder = isset($typeOrder[$b['type']]) ? $typeOrder[$b['type']] : 4;

            if ($aOrder !== $bOrder) {
                return $aOrder - $bOrder;
            }

            // При еднакъв тип и релевантност, сортираме по име
            return strcmp($a['name'], $b['name']);
        });

        // Премахваме полето relevance от крайния резултат
        foreach ($results as &$result) {
            unset($result['relevance']);
            unset($result['type']);
        }

        return $results;
    }
}
