<!-- <PERSON> Banner -->
<section class="relative">
	<div class="swiper-container h-[500px] overflow-hidden">
		<div class="swiper-wrapper">
			{% if slides is defined and slides|length > 0 %}
				{% for slide in slides %}
					<div class="swiper-slide h-full w-full relative">
						{% if slide.image_url %}
							<img src="{{ slide.image_url }}" alt="{{ slide.title|default('') }}" class="w-full h-full object-cover object-top">
						{% endif %}
						<div class="absolute inset-0 bg-black bg-opacity-30 flex items-center">
							<div class="container mx-auto px-4">
								<div class="max-w-lg text-white">
									{% if slide.title %}
										<h1 class="text-4xl font-bold mb-4">{{ slide.title }}</h1>
									{% endif %}
									{% if slide.subtitle %}
										<p class="text-xl mb-6">{{ slide.subtitle }}</p>
									{% endif %}
									{% if slide.buttons is defined and slide.buttons|length > 0 %}
										<div class="flex space-x-4">
											{% for btn in slide.buttons %}
												{% set isPrimary = (btn.style|default('') == 'primary') %}
												{% set btnClass = isPrimary ? 'bg-primary text-white' : 'bg-white text-primary' %}
												{% if btn.action|default('') == 'javascript' and btn.onclick|default('') %}
													<a href="#" onclick="{{ btn.onclick|raw }}; return false;" class="{{ btnClass }} px-6 py-3 rounded-button font-medium hover:bg-opacity-90 whitespace-nowrap">{{ btn.text|default('') }}</a>
												{% else %}
													<a href="{{ btn.href|default('#') }}" class="{{ btnClass }} px-6 py-3 rounded-button font-medium hover:bg-opacity-90 whitespace-nowrap">{{ btn.text|default('') }}</a>
												{% endif %}
											{% endfor %}
										</div>
									{% endif %}
								</div>
							</div>
						</div>
					</div>
				{% endfor %}
			{% endif %}
		</div>
		{# Navigation Arrows #}
		{% if slider_settings is defined and slider_settings.show_arrows %}
			<div class="swiper-button-prev !w-10 !h-10 !bg-black/20 !rounded-full !text-white hover:!bg-black/30 transition-all !left-4">
				<i class="ri-arrow-left-s-line ri-lg"></i>
			</div>
			<div class="swiper-button-next !w-10 !h-10 !bg-black/20 !rounded-full !text-white hover:!bg-black/30 transition-all !right-4">
				<i class="ri-arrow-right-s-line ri-lg"></i>
			</div>
		{% endif %}
		{# Pagination #}
		{% if slider_settings is defined and slider_settings.show_dots %}
			<div class="swiper-pagination !bottom-4"></div>
		{% endif %}

        <script>
            window.heroBannerSettings = {% if heroBannerSettings is defined %}{{ heroBannerSettings|json_encode }}{% endif %};
        </script>
		<style>
			.swiper-pagination-bullet {
				background: rgba(255, 255, 255, 0.6) !important;
				opacity: 1 !important;
			}
			.swiper-pagination-bullet-active {
				background: #9000A7 !important;
			}
		</style>
	</div>
</section>
{# Автоматично инициализиране на Swiper, ако намери .swiper-container е във frontend.js #}