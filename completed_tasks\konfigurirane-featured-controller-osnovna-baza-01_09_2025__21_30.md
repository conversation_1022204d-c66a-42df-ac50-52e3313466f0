# Конфигуриране на Featured контролера за използване на основната база данни - 01/09/2025 21:30

## Първоначален промпт (подканата)

В контролера `F:\Web\Rakla.bg - NEW\system\storage\theme\Backend\Controller\Catalog\Product\Featured.php` трябва да се конфигурира използването на основната (първа) база данни вместо втората база данни.

**Контекст**: Според предишните разговори в проекта се използват две бази данни - основна и втора, като модулите се конфигурират чрез .env файл за да определят коя база данни да използват.

## Резултат от извършената задача

### 🔍 **Анализ на проблема:**

**Проблем**: Контролерът `Featured.php` се намира в namespace `Theme25\Backend\Controller\Catalog\Product\Featured`, което се преобразува в route `catalog/product/featured`. В .env файла `catalog/product` е в списъка `SECOND_DB_MODULES`, което означава че контролерът Featured използва втората база данни.

**Причина**: Липсва механизъм за изключения - модули които трябва да използват основната база данни, дори ако техният parent route е в `SECOND_DB_MODULES`.

### ✅ **Решение:**

#### 1. **Добавена нова конфигурация в .env файла:**

```env
# Модули, които трябва да използват основната база данни (изключения от SECOND_DB_MODULES)
# Разделени със запетая, без интервали
FIRST_DB_MODULES=catalog/product/featured
```

#### 2. **Разширен BaseProcessor.php:**

**Добавена нова статична променлива:**
```php
/**
 * Модули, които трябва да използват основната база данни (изключения от $secondDbModules)
 *
 * @var array
 */
protected static $firstDbModules = [];
```

**Добавено зареждане на FIRST_DB_MODULES от .env:**
```php
// Зареждане на модулите, които трябва да използват основната база данни
self::$firstDbModules = $this->envLoader->getArray('FIRST_DB_MODULES');
```

**Модифициран shouldUseSecondDb() метод:**
```php
protected function shouldUseSecondDb($route) {
    if (self::$secondDb === null) {
        return false;
    }

    // Първо проверяваме дали модулът е в списъка за основната база данни (изключения)
    foreach (self::$firstDbModules as $module) {
        if (strpos(strtolower($route), strtolower($module)) === 0) {
            return false; // Принудително използване на основната база данни
        }
    }

    // След това проверяваме дали модулът е в списъка за втората база данни
    foreach (self::$secondDbModules as $module) {
        if (strpos(strtolower($route), strtolower($module)) === 0) {
            return true;
        }
    }

    return false;
}
```

#### 3. **Подобрен RequestProcessor.php:**

**Добавено принудително превключване към основната база данни:**
```php
else {
    // Убеждаваме се че използваме основната база данни и нейната конфигурация
    $this->switchToFirstDatabase($this->registry);
    
    if($flag) {
        F()->log->developer('Not switched to second db', __FILE__, __LINE__);
    }
}
```

#### 4. **Подобрен ModelProcessor.php:**

**Добавено принудително превключване към основната база данни:**
```php
protected function switchDatabaseIfNeeded($useSecondDb)
{
    if ($useSecondDb) {
        $originalDb = $this->registry->get('db');
        $this->registry->set('db', self::$secondDb);

        // Указване, че в момента се използва втората база данни
        $this->switchToSecondDatabase($this->registry);

        return $originalDb;
    } else {
        // Убеждаваме се че използваме основната база данни и нейната конфигурация
        $this->switchToFirstDatabase($this->registry);
    }

    return null;
}
```

### 🧪 **Тестване на конфигурацията:**

Създаден и изпълнен тест който потвърждава правилната работа:

| Route | Очакван резултат | Действителен резултат | Статус |
|-------|------------------|----------------------|--------|
| `catalog/product` | Втора база данни | Втора база данни | ✅ УСПЕШЕН |
| `catalog/product/featured` | Основна база данни | Основна база данни | ✅ УСПЕШЕН |
| `catalog/category` | Втора база данни | Втора база данни | ✅ УСПЕШЕН |
| `common/dashboard` | Основна база данни | Основна база данни | ✅ УСПЕШЕН |

### 🔧 **Ключови технически детайли:**

#### **Приоритет на проверките:**
1. **Първо**: Проверка за `FIRST_DB_MODULES` (изключения) → връща `false`
2. **Второ**: Проверка за `SECOND_DB_MODULES` → връща `true` ако намери съвпадение
3. **По подразбиране**: Връща `false` (основна база данни)

#### **ID на езика:**
- **Основна база данни**: `config_language_id = 1`
- **Втора база данни**: `config_language_id = 2`

#### **Конфигурация на Featured контролера:**
- **Route**: `catalog/product/featured`
- **База данни**: Основна (първа)
- **Language ID**: 1
- **Модели**: Използват основната база данни и `config_language_id = 1`

### 📁 **Модифицирани файлове:**

1. **system/storage/theme/.env**
   - Добавена конфигурация `FIRST_DB_MODULES=catalog/product/featured`

2. **system/storage/theme/BaseProcessor.php**
   - Backup: `BaseProcessor_2025-09-01_2101.php`
   - Добавена `$firstDbModules` статична променлива
   - Добавено зареждане на `FIRST_DB_MODULES` от .env
   - Модифициран `shouldUseSecondDb()` метод за поддръжка на изключения

3. **system/storage/theme/RequestProcessor.php**
   - Добавено принудително превключване към основната база данни в `else` блока

4. **system/storage/theme/ModelProcessor.php**
   - Добавено принудително превключване към основната база данни в `switchDatabaseIfNeeded()`

### ✅ **Резултат:**

Контролерът `Featured.php` сега:
1. **Използва основната база данни** вместо втората
2. **Работи с `config_language_id = 1`** за правилно извличане на продуктови данни
3. **Запазва всички функционалности** без промени в собствения си код
4. **Автоматично се превключва** към правилната база данни и конфигурация

### 🎯 **Заключение:**

Успешно конфигуриран механизъм за изключения, който позволява на специфични контролери да използват основната база данни, дори ако техният parent route е конфигуриран за втората база данни. Решението е гъвкаво и позволява лесно добавяне на нови изключения чрез .env файла.
