(function(){
  'use strict';

  // Разширяваме BackendModule
  const ProductFeaturedModule = Object.create(BackendModule);

  Object.assign(ProductFeaturedModule, {
    config: {
      maxItems: 4,
      debounceMs: 300
    },

    init: function() {
      const search = document.getElementById('featured-search');
      const results = document.getElementById('featured-search-results');
      const selected = document.getElementById('featured-selected');
      const saveBtn = document.getElementById('featured-save');
      if(!search || !results || !selected || !saveBtn) return;

      this.state = {items: this.readSelected(selected)};

      // Търсене с debounce
      let t;
      search.addEventListener('input', (e) => {
        clearTimeout(t);
        const q = e.target.value.trim();
        if(q.length < 2){ results.classList.add('hidden'); results.innerHTML=''; return; }
        t = setTimeout(() => this.searchProducts(q, results), this.config.debounceMs);
      });

      // Премахване на избран
      selected.addEventListener('click', (e) => {
        const btn = e.target.closest('.remove-selected');
        if(!btn) return;
        const id = parseInt(btn.getAttribute('data-id'), 10);
        this.state.items = this.state.items.filter(i => i.product_id !== id);
        btn.closest('[data-id]')?.remove();
        this.updateStateFromDom();
      });

      // Инициализиране на drag & drop
      this.attachDnd(selected);

      // Запис
      saveBtn.addEventListener('click', () => this.save());
    },

    readSelected: function(container){
      const items = [];
      container.querySelectorAll('[data-id]').forEach(el => {
        items.push({product_id: parseInt(el.getAttribute('data-id'), 10)});
      });
      return items;
    },

    updateStateFromDom: function(){
      const container = document.getElementById('featured-selected');
      const items = [];
      container.querySelectorAll('[data-id]').forEach(el => {
        items.push({product_id: parseInt(el.getAttribute('data-id'), 10)});
      });
      this.state.items = items;
    },

    attachDnd: function(container){
      if(this._dndAttached) return;
      this._dndAttached = true;
      let draggingEl = null;

      container.addEventListener('dragstart', (e) => {
        const item = e.target.closest('[data-id]');
        if(!item) return e.preventDefault();
        // позволяваме drag само при хващане на handle
        if(!e.target.closest('.drag-handle')){ e.preventDefault(); return; }
        draggingEl = item;
        item.classList.add('opacity-50','ring-2','ring-primary');
        e.dataTransfer.effectAllowed = 'move';
      });

      container.addEventListener('dragover', (e) => {
        e.preventDefault();
        if(!draggingEl) return;
        const afterElement = this.getDragAfterElement(container, e.clientY, draggingEl);
        if(afterElement == null){
          container.appendChild(draggingEl);
        } else if(afterElement !== draggingEl) {
          container.insertBefore(draggingEl, afterElement);
        }
      });

      container.addEventListener('drop', (e) => {
        e.preventDefault();
        if(!draggingEl) return;
        draggingEl.classList.remove('opacity-50','ring-2','ring-primary');
        draggingEl = null;
        this.updateStateFromDom();
      });

      container.addEventListener('dragend', () => {
        if(draggingEl){ draggingEl.classList.remove('opacity-50','ring-2','ring-primary'); }
        draggingEl = null;
        this.updateStateFromDom();
      });
    },

    getDragAfterElement: function(container, y, draggingEl){
      const draggableElements = [...container.querySelectorAll('[data-id]')]
        .filter(el => el !== draggingEl);
      let closest = null;
      let closestOffset = Number.NEGATIVE_INFINITY;
      draggableElements.forEach(child => {
        const box = child.getBoundingClientRect();
        const offset = y - box.top - box.height / 2;
        if (offset < 0 && offset > closestOffset) {
          closestOffset = offset;
          closest = child;
        }
      });
      return closest;
    },

    searchProducts: function(q, resultsEl){
      const url = this.buildAdminUrl('catalog/product/autocomplete', { filter_name: q });
      fetch(url, { headers: {'X-Requested-With': 'XMLHttpRequest'} })
        .then(r => r.json())
        .then(json => {
          resultsEl.innerHTML = '';
          resultsEl.classList.remove('hidden');
          (json || []).slice(0, 10).forEach(item => {
            const div = document.createElement('div');
            div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer';
            div.textContent = item.name;
            div.addEventListener('click', () => this.addSelected(item));
            resultsEl.appendChild(div);
          });
        })
        .catch(() => { resultsEl.classList.add('hidden'); });
    },

    addSelected: function(item){
      if(this.state.items.find(i => i.product_id === item.product_id)) return;
      if(this.state.items.length >= this.config.maxItems) { alert('Може да изберете максимум 4 продукта.'); return; }

      this.state.items.push({product_id: item.product_id});
      const container = document.getElementById('featured-selected');
      const div = document.createElement('div');
      div.className = 'flex items-center justify-between border rounded px-3 py-2 bg-white';
      div.setAttribute('data-id', item.product_id);
      div.setAttribute('draggable', 'true');
      div.innerHTML = `<div class="flex items-center gap-2"><span class="drag-handle cursor-move select-none text-gray-400" title="Премести">⋮⋮</span><span>${item.name}</span></div><button class="text-red-600 remove-selected" data-id="${item.product_id}">Премахни</button>`;
      container.appendChild(div);
      this.updateStateFromDom();
    },

    save: function(){
      if(this.state.items.length !== this.config.maxItems){ alert('Трябва да изберете точно 4 продукта.'); return; }
      const body = new URLSearchParams();
      this.state.items.forEach((i, idx) => {
        body.append('selected_products[]', i.product_id);
        body.append('sort_order[]', idx);
      });

      fetch(window.ProductFeaturedConfig.submitUrl, {
        method: 'POST',
        headers: { 'X-Requested-With': 'XMLHttpRequest', 'Content-Type': 'application/x-www-form-urlencoded' },
        body: body.toString()
      })
      .then(r => r.json())
      .then(json => {
        if(json && json.success){ alert('Записът е успешен.'); }
        else { alert('Грешка: ' + (json.error || 'неизвестна')); }
      })
      .catch(() => alert('Възникна грешка при запис.'));
    }
  });

  document.addEventListener('DOMContentLoaded', function(){
    if(window.BackendModule){ ProductFeaturedModule.init(); }
  });
})();

