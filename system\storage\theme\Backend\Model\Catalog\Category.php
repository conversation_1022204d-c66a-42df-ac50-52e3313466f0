<?php

namespace Theme25\Backend\Model\Catalog;

// Включване на стандартния модел
require_once(DIR_APPLICATION . 'model/catalog/category.php');

class Category extends \ModelCatalogCategory {

   public function getCategories($data = array()) {
		// Ако има филтър по име, използваме разширена логика за включване на подкатегории
		if (!empty($data['filter_name'])) {
			$sql = "SELECT
				cp.category_id AS category_id,
				GROUP_CONCAT(cd1.name ORDER BY cp.level SEPARATOR ' > ') AS name,
				c1.parent_id,
				c1.sort_order,
				c1.status
			FROM " . DB_PREFIX . "category_path cp
			LEFT JOIN " . DB_PREFIX . "category c1
				ON (cp.category_id = c1.category_id)
			LEFT JOIN " . DB_PREFIX . "category c2
				ON (cp.path_id = c2.category_id)
			LEFT JOIN " . DB_PREFIX . "category_description cd1
				ON (cp.path_id = cd1.category_id)
			LEFT JOIN " . DB_PREFIX . "category_description cd2
				ON (cp.category_id = cd2.category_id)
			WHERE cd1.language_id = '" . (int)$this->config->get('config_language_id') . "'
			AND cd2.language_id = '" . (int)$this->config->get('config_language_id') . "'
			AND (
				-- Включваме категории, които директно съответстват на търсения термин
				cd2.name LIKE '%" . $this->db->escape($data['filter_name']) . "%'
				OR
				-- Включваме всички подкатегории на категории, които съответстват на търсения термин
				cp.category_id IN (
					SELECT DISTINCT cp_child.category_id
					FROM " . DB_PREFIX . "category_path cp_child
					INNER JOIN " . DB_PREFIX . "category_path cp_match
						ON (cp_child.path_id = cp_match.category_id)
					INNER JOIN " . DB_PREFIX . "category_description cd_match
						ON (cp_match.category_id = cd_match.category_id)
					WHERE cd_match.language_id = '" . (int)$this->config->get('config_language_id') . "'
					AND cd_match.name LIKE '%" . $this->db->escape($data['filter_name']) . "%'
					AND cp_child.category_id != cp_match.category_id
				)
			)";
		} else {
			// Стандартна логика без филтър по име
			$sql = "SELECT
				cp.category_id AS category_id,
				GROUP_CONCAT(cd1.name ORDER BY cp.level SEPARATOR ' > ') AS name,
				c1.parent_id,
				c1.sort_order,
				c1.status
			FROM " . DB_PREFIX . "category_path cp
			LEFT JOIN " . DB_PREFIX . "category c1
				ON (cp.category_id = c1.category_id)
			LEFT JOIN " . DB_PREFIX . "category c2
				ON (cp.path_id = c2.category_id)
			LEFT JOIN " . DB_PREFIX . "category_description cd1
				ON (cp.path_id = cd1.category_id)
			LEFT JOIN " . DB_PREFIX . "category_description cd2
				ON (cp.category_id = cd2.category_id)
			WHERE cd1.language_id = '" . (int)$this->config->get('config_language_id') . "'
			AND cd2.language_id = '" . (int)$this->config->get('config_language_id') . "'";
		}

        if (isset($data['filter_parent_id'])) {
			$sql .= " AND c1.parent_id = '" . (int)$data['filter_parent_id'] . "'";
		}

		if (!empty($data['exclude_category_id'])) {
			$sql .= " AND cp.category_id != '" . (int)$data['exclude_category_id'] . "'";
		}

		if (!empty($data['exclude_parent_id'])) {
			$sql .= " AND cp.category_id != '" . (int)$data['exclude_parent_id'] . "'";
		}

		$sql .= " GROUP BY cp.category_id";

		$sort_data = array(
			'name',
			'sort_order'
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY sort_order";
		}

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalCategories($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "category c1 LEFT JOIN " . DB_PREFIX . "category_description cd1 ON (c1.category_id = cd1.category_id)";

		if (!empty($data['filter_name'])) {
			$sql .= " AND cd1.name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (isset($data['filter_parent_id'])) {
			$sql .= " AND c1.parent_id = '" . (int)$data['filter_parent_id'] . "'";
		}

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function editCategory($category_id, $data) {

		// Актуализиране на основната таблица 'category'
		$this->db->query("UPDATE " . DB_PREFIX . "category SET
			parent_id = '" . (int)$data['parent_id'] . "',
			status = '" . (int)$data['status'] . "',
			image = '" . $this->db->escape($data['image']) . "'
			WHERE category_id = '" . (int)$category_id . "'");

		// Обновяване или вмъкване на описания за 'category_description'
		foreach ($data['category_description'] as $language_id => $value) {
			$this->db->query("REPLACE INTO " . DB_PREFIX . "category_description SET
				category_id = '" . (int)$category_id . "',
				language_id = '" . (int)$language_id . "',
				name = '" . $this->db->escape($value['name']) . "',
				description = '" . $this->db->escape($value['description']) . "',
				meta_title = '" . $this->db->escape($value['meta_title']) . "',
				meta_keyword = '" . $this->db->escape($value['meta_keyword']) . "',
				meta_description = '" . $this->db->escape($value['meta_description']) . "'");
		}

		// Обновяване или вмъкване на асоциации с магазини за 'category_to_store'
		if (isset($data['category_store'])) {
			foreach ($data['category_store'] as $store_id) {
				$this->db->query("REPLACE INTO " . DB_PREFIX . "category_to_store SET
					category_id = '" . (int)$category_id . "',
					store_id = '" . (int)$store_id . "'");
			}
		}

		// Обновяване или вмъкване на SEO URL адреси за 'seo_url'
		if (isset($data['category_seo_url'])) {
			foreach ($data['category_seo_url'] as $language_id => $keyword) {
				if ($keyword) {
					$this->db->query("REPLACE INTO " . DB_PREFIX . "seo_url SET
						store_id = '0',
						language_id = '" . (int)$language_id . "',
						query = 'category_id=" . (int)$category_id . "',
						keyword = '" . $this->db->escape($keyword) . "'");
				}
			}
		}

		// Изтриване на стари филтри и вмъкване на нови за 'category_filter'
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_filter WHERE category_id = '" . (int)$category_id . "'");
		if (isset($data['category_filter'])) {
			foreach ($data['category_filter'] as $filter_id) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "category_filter SET category_id = '" . (int)$category_id . "', filter_id = '" . (int)$filter_id . "'");
			}
		}

		// Изтриване на стари асоциации с оформления и вмъкване на нови за 'category_to_layout'
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_to_layout WHERE category_id = '" . (int)$category_id . "'");
		if (isset($data['category_layout'])) {
			foreach ($data['category_layout'] as $store_id => $layout_id) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "category_to_layout SET category_id = '" . (int)$category_id . "', store_id = '" . (int)$store_id . "', layout_id = '" . (int)$layout_id . "'");
			}
		}

		// Изтриване на стари пътища и вмъкване на нови за 'category_path'
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_path WHERE category_id = '" . (int)$category_id . "'");

		$level = 0;
		if (isset($data['parent_id']) && $data['parent_id'] > 0) {
			$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "category_path WHERE category_id = '" . (int)$data['parent_id'] . "' ORDER BY level ASC");

			foreach ($query->rows as $result) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "category_path SET category_id = '" . (int)$category_id . "', path_id = '" . (int)$result['path_id'] . "', level = '" . (int)$level . "'");
				$level++;
			}
		}

		$this->db->query("INSERT INTO " . DB_PREFIX . "category_path SET category_id = '" . (int)$category_id . "', path_id = '" . (int)$category_id . "', level = '" . (int)$level . "'");

		$this->cache->delete('category');
	}

	public function addCategory($data) {
		// Вмъкване на данни в основната таблица 'category'
		$this->db->query("INSERT INTO " . DB_PREFIX . "category SET
			parent_id = '" . (int)$data['parent_id'] . "',
			`top` = '" . (isset($data['top']) ? (int)$data['top'] : 0) . "',
			`column` = '" . (isset($data['column']) ? (int)$data['column'] : 0) . "',
			sort_order = '" . (int)$data['sort_order'] . "',
			status = '" . (int)$data['status'] . "',
			image = '" . $this->db->escape($data['image']) . "',
			date_added = NOW(),
			date_modified = NOW()");

		$category_id = $this->db->getLastId();

		// Вмъкване на описания за 'category_description'
		foreach ($data['category_description'] as $language_id => $value) {
			$this->db->query("INSERT INTO " . DB_PREFIX . "category_description SET
				category_id = '" . (int)$category_id . "',
				language_id = '" . (int)$language_id . "',
				name = '" . $this->db->escape($value['name']) . "',
				description = '" . $this->db->escape($value['description']) . "',
				meta_title = '" . $this->db->escape($value['meta_title']) . "',
				meta_keyword = '" . $this->db->escape($value['meta_keyword']) . "',
				meta_description = '" . $this->db->escape($value['meta_description']) . "'");
		}

		// Вмъкване на асоциации с магазини за 'category_to_store'
		if (isset($data['category_store'])) {
			foreach ($data['category_store'] as $store_id) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "category_to_store SET
					category_id = '" . (int)$category_id . "',
					store_id = '" . (int)$store_id . "'");
			}
		}

		// Вмъкване на SEO URL адреси за 'seo_url'
		if (isset($data['category_seo_url'])) {
			foreach ($data['category_seo_url'] as $language_id => $keyword) {
				if ($keyword) {
					$this->db->query("INSERT INTO " . DB_PREFIX . "seo_url SET
						store_id = '0',
						language_id = '" . (int)$language_id . "',
						query = 'category_id=" . (int)$category_id . "',
						keyword = '" . $this->db->escape($keyword) . "'");
				}
			}
		}

		// Вмъкване на филтри за 'category_filter'
		if (isset($data['category_filter'])) {
			foreach ($data['category_filter'] as $filter_id) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "category_filter SET category_id = '" . (int)$category_id . "', filter_id = '" . (int)$filter_id . "'");
			}
		}

		// Вмъкване на асоциации с оформления за 'category_to_layout'
		if (isset($data['category_layout'])) {
			foreach ($data['category_layout'] as $store_id => $layout_id) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "category_to_layout SET category_id = '" . (int)$category_id . "', store_id = '" . (int)$store_id . "', layout_id = '" . (int)$layout_id . "'");
			}
		}

		// Вмъкване на пътища за 'category_path'
		$level = 0;
		if (isset($data['parent_id']) && $data['parent_id'] > 0) {
			$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "category_path WHERE category_id = '" . (int)$data['parent_id'] . "' ORDER BY level ASC");

			foreach ($query->rows as $result) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "category_path SET category_id = '" . (int)$category_id . "', path_id = '" . (int)$result['path_id'] . "', level = '" . (int)$level . "'");
				$level++;
			}
		}

		$this->db->query("INSERT INTO " . DB_PREFIX . "category_path SET category_id = '" . (int)$category_id . "', path_id = '" . (int)$category_id . "', level = '" . (int)$level . "'");

		$this->cache->delete('category');

		return $category_id;
	}

	public function deleteCategory($category_id) {
		$this->db->query("DELETE FROM " . DB_PREFIX . "category WHERE category_id = '" . (int)$category_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_description WHERE category_id = '" . (int)$category_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_to_store WHERE category_id = '" . (int)$category_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_filter WHERE category_id = '" . (int)$category_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_path WHERE category_id = '" . (int)$category_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_to_layout WHERE category_id = '" . (int)$category_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "seo_url WHERE query = 'category_id=" . (int)$category_id . "'");
	}

	public function deleteCategories($category_ids) {
		$this->db->query("DELETE FROM " . DB_PREFIX . "category WHERE category_id IN (" . implode(',', $category_ids) . ")");
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_description WHERE category_id IN (" . implode(',', $category_ids) . ")");
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_to_store WHERE category_id IN (" . implode(',', $category_ids) . ")");
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_filter WHERE category_id IN (" . implode(',', $category_ids) . ")");
		$this->db->query("DELETE FROM " . DB_PREFIX . "category_to_layout WHERE category_id IN (" . implode(',', $category_ids) . ")");

		// За seo_url и category_path използваме LIKE/повторение, защото query е 'category_id=X' или пътищата са индивидуални
		foreach ($category_ids as $category_id) {
			$this->db->query("DELETE FROM " . DB_PREFIX . "seo_url WHERE query LIKE 'category_id=" . (int)$category_id . "'");
			$this->db->query("DELETE FROM " . DB_PREFIX . "category_path WHERE path_id = '" . (int)$category_id . "' OR category_id = '" . (int)$category_id . "'");
		}
	}

	public function isSeoUrlUnique($keyword, $language_id, $store_id, $category_id = 0) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "seo_url WHERE keyword = '" . $this->db->escape($keyword) . "' AND language_id = '" . (int)$language_id . "' AND store_id = '" . (int)$store_id . "' AND query LIKE 'category_id=%'";

		if ($category_id) {
			$sql .= " AND query != 'category_id=" . (int)$category_id . "'";
		}

		$query = $this->db->query($sql);

		return $query->row['total'] == 0;
	}

	/**
	 * Търсене в категории с case-insensitive заявка
	 *
	 * @param string $searchTerm Търсен термин
	 * @param int $limit Максимален брой резултати
	 * @param int $offset Отместване за пагинация
	 * @return array Масив с резултати
	 */
	public function searchCategories($searchTerm, $limit = 10, $offset = 0) {
		$sql = "SELECT c.category_id as id, cd.name as name
				FROM " . DB_PREFIX . "category c
				LEFT JOIN " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
				WHERE cd.language_id = '" . (int)$this->config->get('config_language_id') . "'
				AND c.status = '1'
				AND LOWER(cd.name) LIKE '%" . $this->db->escape(mb_strtolower($searchTerm)) . "%'
				ORDER BY cd.name ASC
				LIMIT " . (int)$limit . " OFFSET " . (int)$offset;

		$query = $this->db->query($sql);

		return $query->rows;
	}

	/**
	 * Броене на общия брой категории за търсен термин
	 *
	 * @param string $searchTerm Търсен термин
	 * @return int Общ брой резултати
	 */
	public function countSearchCategories($searchTerm) {
		$sql = "SELECT COUNT(*) as total
				FROM " . DB_PREFIX . "category c
				LEFT JOIN " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
				WHERE cd.language_id = '" . (int)$this->config->get('config_language_id') . "'
				AND c.status = '1'
				AND LOWER(cd.name) LIKE '%" . $this->db->escape(mb_strtolower($searchTerm)) . "%'";

		$query = $this->db->query($sql);

		return (int)$query->row['total'];
	}
}
