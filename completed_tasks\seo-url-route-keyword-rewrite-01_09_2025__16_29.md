# Prompt
как да променя този файл F:\Web\Rakla.bg - NEW\system\storage\theme\Frontend\Controller\Startup\SeoUrl.php така, че например при 'query' = 'route=account/account', да rewrite-не към domain.com/account/account или каквато `keyword` задам за това `query`

# Извършени промени
- Файл: `system/storage/theme/Frontend/Controller/Startup/SeoUrl.php`
- Добавена е поддръжка за SEO rewrite на маршрути (`route=...`) при генериране на линкове и при разкодиране на входящи SEO URL:
  - В `rewrite($link)`: ако присъства `route`, търси запис в `seo_url` по `query='route=<route>'`, а при липса – fallback към `query='<route>'`. При намерен `keyword` добавя сегмента към SEO пътя.
  - В `index()`: при разкодиране, ако `query` е от вида `route=<route>`, задава `$this->request->get['route'] = <route>`.

# Как да се използва
- В таблица `oc_seo_url` (или префикса от `DB_PREFIX`) добавете редове например:
  - `query = 'route=account/account'`, `keyword = 'account/account'`, `store_id = <id>`, `language_id = <id>`
  - (по избор) алтернативно може `query = 'account/account'` със същия `keyword`.
- След това извикване на `$this->url->link('account/account')` (или `getLink('account/account')` в Theme контролер) ще генерира `/account/account`.

# Бележки
- Съобразено с PHP 7.4.
- Не са променяни останалите rewrite правила (product/category/manufacturer/information).

# Тестови стъпки
1) Запишете запис в `seo_url` за `route=account/account` с желан keyword.
2) Генерирайте линк: `getLink('account/account')` и проверете, че URL е SEO.
3) Отворете `/account/account` и очаквайте да се разкодира до `route=account/account`.
