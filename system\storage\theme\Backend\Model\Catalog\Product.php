<?php

namespace Theme25\Backend\Model\Catalog;

// Включване на стандартния модел
require_once(DIR_APPLICATION . 'model/catalog/product.php');

class Product extends \ModelCatalogProduct {

    /**
     * Получаване на продукт по ID - връща всички данни включително името от product_description
     * Преопределяме метода от родителския клас
     */
    public function getProduct($product_id) {
        $query = $this->db->query("
            SELECT DISTINCT p.*, pd.name, pd.description, pd.tag, pd.meta_title, pd.meta_description, pd.meta_keyword
            FROM " . DB_PREFIX . "product p
            LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
            WHERE p.product_id = '" . (int)$product_id . "'
            AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
        ");

        return $query->row;
    }

    /**
     * Получаване на продукти с възможност за изключване на определен продукт
     * Разширяваме метода от родителския клас
     */
    public function getProducts($data = array()) {
        $sql = "SELECT p.*, pd.*,
                (SELECT ps.price FROM " . DB_PREFIX . "product_special ps
                 WHERE ps.product_id = p.product_id
                 AND ps.customer_group_id = '" . (int)$this->config->get('config_customer_group_id') . "'
                 AND ((ps.date_start = '0000-00-00' OR ps.date_start <= CURDATE())
                 AND (ps.date_end = '0000-00-00' OR ps.date_end >= CURDATE()))
                 ORDER BY ps.priority ASC, ps.price ASC LIMIT 1) as special
                FROM " . DB_PREFIX . "product p
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE pd.language_id = '" . (int)$this->config->get('config_language_id') . "'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND LOWER(pd.name) LIKE '%" . strtolower($this->db->escape(mb_strtolower($data['filter_name']))) . "%'";
        }

        if (!empty($data['filter_model'])) {
            $sql .= " AND LOWER(p.model) LIKE '%" . strtolower($this->db->escape(mb_strtolower($data['filter_model']))) . "%'";
        }

        if (!empty($data['filter_price_min'])) {
            $sql .= " AND p.price >= '" . (float)$data['filter_price_min'] . "'";
        }

        if (!empty($data['filter_price_max'])) {
            $sql .= " AND p.price <= '" . (float)$data['filter_price_max'] . "'";
        }

        if (isset($data['filter_status']) && $data['filter_status'] !== '') {
            $sql .= " AND p.status = '" . (int)$data['filter_status'] . "'";
        }

        if (!empty($data['filter_category_id'])) {
            $sql .= " AND p.product_id IN (SELECT product_id FROM " . DB_PREFIX . "product_to_category WHERE category_id = '" . (int)$data['filter_category_id'] . "')";
        }

        if (!empty($data['filter_date_start'])) {
            $sql .= " AND DATE(p.date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
        }

        if (!empty($data['filter_date_end'])) {
            $sql .= " AND DATE(p.date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
        }

        if (!empty($data['filter_price'])) {
            $sql .= " AND p.price LIKE '%" . $this->db->escape($data['filter_price']) . "%'";
        }

        if (isset($data['filter_quantity']) && $data['filter_quantity'] !== '') {
            $sql .= " AND p.quantity = '" . (int)$data['filter_quantity'] . "'";
        }
        
        if (!empty($data['filter_category_id'])) {
            $sql .= " AND p.product_id IN (SELECT product_id FROM " . DB_PREFIX . "product_to_category WHERE category_id = '" . (int)$data['filter_category_id'] . "')";
        }

        if (isset($data['filter_status']) && $data['filter_status'] !== '') {
            $sql .= " AND p.status = '" . (int)$data['filter_status'] . "'";
        }

        // Филтриране по активни промоции
        if (isset($data['filter_special']) && $data['filter_special'] == '1') {
            $sql .= " AND p.product_id IN (
                SELECT DISTINCT ps.product_id
                FROM " . DB_PREFIX . "product_special ps
                WHERE ps.product_id = p.product_id
                AND ps.customer_group_id = '" . (int)$this->config->get('config_customer_group_id') . "'
                AND ((ps.date_start = '0000-00-00' OR ps.date_start <= CURDATE())
                AND (ps.date_end = '0000-00-00' OR ps.date_end >= CURDATE()))
            )";
        }

        // Изключване на определен продукт от резултатите
        if (!empty($data['exclude_product_id'])) {
            $sql .= " AND p.product_id != '" . (int)$data['exclude_product_id'] . "'";
        }

        $sql .= " GROUP BY p.product_id";

        $sort_data = array(
            'pd.name',
            'p.model',
            'p.price',
            'p.quantity',
            'p.status',
            'p.sort_order',
            'p.product_id'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            // $sql .= " ORDER BY pd.name";
            $sql .= " ORDER BY p.product_id";
        }

        if (isset($data['order']) ) {
            $sql .= " " . $data['order'];
        } else {
            $sql .= " DESC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        $query = $this->db->query($sql);
        return $query->rows;
    }

    public function getTotalProducts($data = array()) {
		$sql = "SELECT COUNT(DISTINCT p.product_id) AS total FROM " . DB_PREFIX . "product p LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)";

		$sql .= " WHERE pd.language_id = '" . (int)$this->config->get('config_language_id') . "'";

		if (!empty($data['filter_name'])) {
			$sql .= " AND LOWER(pd.name) LIKE '%" . strtolower($this->db->escape(mb_strtolower($data['filter_name']))) . "%'";
		}

		if (!empty($data['filter_model'])) {
			$sql .= " AND LOWER(p.model) LIKE '%" . strtolower($this->db->escape(mb_strtolower($data['filter_model']))) . "%'";
		}

		if (!empty($data['filter_price_min'])) {
			$sql .= " AND p.price >= '" . (float)$data['filter_price_min'] . "'";
		}

		if (!empty($data['filter_price_max'])) {
			$sql .= " AND p.price <= '" . (float)$data['filter_price_max'] . "'";
		}

		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$sql .= " AND p.status = '" . (int)$data['filter_status'] . "'";
		}

		if (!empty($data['filter_category_id'])) {
			$sql .= " AND p.product_id IN (SELECT product_id FROM " . DB_PREFIX . "product_to_category WHERE category_id = '" . (int)$data['filter_category_id'] . "')";
		}

		if (!empty($data['filter_date_start'])) {
			$sql .= " AND DATE(p.date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
		}

		if (!empty($data['filter_date_end'])) {
			$sql .= " AND DATE(p.date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
		}

		// ВАЖНО: Добавяне на филтър за промоции (липсваше!)
		if (isset($data['filter_special']) && $data['filter_special'] == '1') {
			$sql .= " AND p.product_id IN (
				SELECT DISTINCT ps.product_id
				FROM " . DB_PREFIX . "product_special ps
				WHERE ps.product_id = p.product_id
				AND ps.customer_group_id = '" . (int)$this->config->get('config_customer_group_id') . "'
				AND ((ps.date_start = '0000-00-00' OR ps.date_start <= CURDATE())
				AND (ps.date_end = '0000-00-00' OR ps.date_end >= CURDATE()))
			)";
		}

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

    public function addProduct($data) {
        // Основна информация за продукта
        $this->db->query("INSERT INTO " . DB_PREFIX . "product SET 
            model = '" . $this->db->escape($data['model']) . "', 
            sku = '" . $this->db->escape($data['sku']) . "', 
            upc = '" . $this->db->escape($data['upc']) . "', 
            ean = '" . $this->db->escape($data['ean']) . "', 
            jan = '" . $this->db->escape($data['jan']) . "', 
            isbn = '" . $this->db->escape($data['isbn']) . "', 
            mpn = '" . $this->db->escape($data['mpn']) . "', 
            location = '" . $this->db->escape($data['location']) . "', 
            quantity = '" . (int)$data['quantity'] . "', 
            minimum = '" . (int)$data['minimum'] . "', 
            subtract = '" . (int)$data['subtract'] . "', 
            stock_status_id = '" . (int)$data['stock_status_id'] . "', 
            date_available = '" . $this->db->escape($data['date_available']) . "', 
            manufacturer_id = '" . (int)$data['manufacturer_id'] . "', 
            shipping = '" . (int)$data['shipping'] . "', 
            price = '" . (float)$data['price'] . "', 
            points = '" . (int)$data['points'] . "', 
            weight = '" . (float)$data['weight'] . "', 
            weight_class_id = '" . (int)$data['weight_class_id'] . "', 
            length = '" . (float)$data['length'] . "', 
            width = '" . (float)$data['width'] . "', 
            height = '" . (float)$data['height'] . "', 
            length_class_id = '" . (int)$data['length_class_id'] . "', 
            status = '" . (int)$data['status'] . "', 
            tax_class_id = '" . (int)$data['tax_class_id'] . "', 
            sort_order = '" . (int)$data['sort_order'] . "', 
            date_added = NOW()");

        $product_id = $this->db->getLastId();

        // Добавяне на основно изображение, ако е зададено
        if (isset($data['image'])) {
            $this->db->query("UPDATE " . DB_PREFIX . "product SET 
                image = '" . $this->db->escape($data['image']) . "' 
                WHERE product_id = '" . (int)$product_id . "'");
        }

        // Добавяне на описания за всеки език
        foreach ($data['product_description'] as $language_id => $value) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "product_description SET 
                product_id = '" . (int)$product_id . "', 
                language_id = '" . (int)$language_id . "', 
                name = '" . $this->db->escape($value['name']) . "', 
                description = '" . $this->db->escape($value['description']) . "', 
                tag = '" . $this->db->escape($value['tag']) . "', 
                meta_title = '" . $this->db->escape($value['meta_title']) . "', 
                meta_description = '" . $this->db->escape($value['meta_description']) . "', 
                meta_keyword = '" . $this->db->escape($value['meta_keyword']) . "'");
        }

        // Добавяне на връзки с магазини
        if (isset($data['product_store'])) {
            foreach ($data['product_store'] as $store_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_store SET 
                    product_id = '" . (int)$product_id . "', 
                    store_id = '" . (int)$store_id . "'");
            }
        }

        // Добавяне на атрибути
        if (isset($data['product_attribute'])) {
            foreach ($data['product_attribute'] as $product_attribute) {
                if ($product_attribute['attribute_id']) {
                    // Добавяне на атрибутите за всеки език
                    foreach ($product_attribute['product_attribute_description'] as $language_id => $product_attribute_description) {
                        $this->db->query("INSERT INTO " . DB_PREFIX . "product_attribute SET 
                            product_id = '" . (int)$product_id . "', 
                            attribute_id = '" . (int)$product_attribute['attribute_id'] . "', 
                            language_id = '" . (int)$language_id . "', 
                            text = '" .  $this->db->escape($product_attribute_description['text']) . "'");
                    }
                }
            }
        }

        // Добавяне на опции
        if (isset($data['product_option'])) {
            foreach ($data['product_option'] as $product_option) {
                // Обработка на опции от тип избор (select, radio, checkbox, image)
                if (in_array($product_option['type'], ['select', 'radio', 'checkbox', 'image'])) {
                    if (isset($product_option['product_option_value'])) {
                        $this->db->query("INSERT INTO " . DB_PREFIX . "product_option SET 
                            product_id = '" . (int)$product_id . "', 
                            option_id = '" . (int)$product_option['option_id'] . "', 
                            required = '" . (int)$product_option['required'] . "'");

                        $product_option_id = $this->db->getLastId();

                        // Добавяне на стойностите за опцията
                        foreach ($product_option['product_option_value'] as $product_option_value) {
                            $this->db->query("INSERT INTO " . DB_PREFIX . "product_option_value SET 
                                product_option_id = '" . (int)$product_option_id . "', 
                                product_id = '" . (int)$product_id . "', 
                                option_id = '" . (int)$product_option['option_id'] . "', 
                                option_value_id = '" . (int)$product_option_value['option_value_id'] . "', 
                                quantity = '" . (int)$product_option_value['quantity'] . "', 
                                subtract = '" . (int)$product_option_value['subtract'] . "', 
                                price = '" . (float)$product_option_value['price'] . "', 
                                price_prefix = '" . $this->db->escape($product_option_value['price_prefix']) . "', 
                                points = '" . (int)$product_option_value['points'] . "', 
                                points_prefix = '" . $this->db->escape($product_option_value['points_prefix']) . "', 
                                weight = '" . (float)$product_option_value['weight'] . "', 
                                weight_prefix = '" . $this->db->escape($product_option_value['weight_prefix']) . "'");
                        }
                    }
                } else {
                     // Обработка на прости опции (текст, текстова област и др.)
                    $this->db->query("INSERT INTO " . DB_PREFIX . "product_option SET 
                        product_id = '" . (int)$product_id . "', 
                        option_id = '" . (int)$product_option['option_id'] . "', 
                        value = '" . $this->db->escape($product_option['value']) . "', 
                        required = '" . (int)$product_option['required'] . "'");
                }
            }
        }

        // Добавяне на повтарящи се плащания
        if (isset($data['product_recurring'])) {
            foreach ($data['product_recurring'] as $recurring) {
                $this->db->query("INSERT INTO `" . DB_PREFIX . "product_recurring` SET 
                    `product_id` = " . (int)$product_id . ", 
                    customer_group_id = " . (int)$recurring['customer_group_id'] . ", 
                    `recurring_id` = " . (int)$recurring['recurring_id']);
            }
        }
        
        // Добавяне на отстъпки
        if (isset($data['product_discount'])) {
            foreach ($data['product_discount'] as $product_discount) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_discount SET 
                    product_id = '" . (int)$product_id . "', 
                    customer_group_id = '" . (int)$product_discount['customer_group_id'] . "', 
                    quantity = '" . (int)$product_discount['quantity'] . "', 
                    priority = '" . (int)$product_discount['priority'] . "', 
                    price = '" . (float)$product_discount['price'] . "', 
                    date_start = '" . $this->db->escape($product_discount['date_start']) . "', 
                    date_end = '" . $this->db->escape($product_discount['date_end']) . "'");
            }
        }

        $product_specials = isset($data['product_special']) ? $data['product_special'] : [];
        // Добавяне на нови специални цени
        $this->prepareSpecialData($product_id, $product_specials);

        // Добавяне на допълнителни изображения
        if (isset($data['product_image'])) {
            foreach ($data['product_image'] as $product_image) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_image SET 
                    product_id = '" . (int)$product_id . "', 
                    image = '" . $this->db->escape($product_image['image']) . "', 
                    sort_order = '" . (int)$product_image['sort_order'] . "'");
            }
        }

        // Добавяне на файлове за изтегляне
        if (isset($data['product_download'])) {
            foreach ($data['product_download'] as $download_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_download SET 
                    product_id = '" . (int)$product_id . "', 
                    download_id = '" . (int)$download_id . "'");
            }
        }

        // Добавяне към категории
        if (isset($data['product_category'])) {
            foreach ($data['product_category'] as $category_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_category SET 
                    product_id = '" . (int)$product_id . "', 
                    category_id = '" . (int)$category_id . "'");
            }
        }

        // Добавяне на филтри
        if (isset($data['product_filter'])) {
            foreach ($data['product_filter'] as $filter_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_filter SET 
                    product_id = '" . (int)$product_id . "', 
                    filter_id = '" . (int)$filter_id . "'");
            }
        }

        // Добавяне на свързани продукти (в двете посоки)
        if (isset($data['product_related'])) {
            foreach ($data['product_related'] as $related_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_related SET
                    product_id = '" . (int)$product_id . "',
                    related_id = '" . (int)$related_id . "'");

                $this->db->query("INSERT INTO " . DB_PREFIX . "product_related SET
                    product_id = '" . (int)$related_id . "',
                    related_id = '" . (int)$product_id . "'");
            }
        }

        // Добавяне на допълнителни продукти
        if (isset($data['product_more'])) {
            $this->processAdditionalProducts($product_id, $data['product_more']);
        }

        // Добавяне на наградни точки
        if (isset($data['product_reward'])) {
            foreach ($data['product_reward'] as $customer_group_id => $product_reward) {
                if ((int)$product_reward['points'] > 0) {
                    $this->db->query("INSERT INTO " . DB_PREFIX . "product_reward SET 
                        product_id = '" . (int)$product_id . "', 
                        customer_group_id = '" . (int)$customer_group_id . "', 
                        points = '" . (int)$product_reward['points'] . "'");
                }
            }
        }
        
        // Добавяне на SEO URL адреси
        if (isset($data['product_seo_url'])) {
            foreach ($data['product_seo_url'] as $store_id => $language) {
                foreach ($language as $language_id => $keyword) {
                    if (trim($keyword)) {
                        $this->db->query("INSERT INTO " . DB_PREFIX . "seo_url SET 
                            store_id = '" . (int)$store_id . "', 
                            language_id = '" . (int)$language_id . "', 
                            query = 'product_id=" . (int)$product_id . "', 
                            keyword = '" . $this->db->escape($keyword) . "'");
                    }
                }
            }
        }
        
        // Добавяне на лейаути
        if (isset($data['product_layout'])) {
            foreach ($data['product_layout'] as $store_id => $layout_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_layout SET 
                    product_id = '" . (int)$product_id . "', 
                    store_id = '" . (int)$store_id . "', 
                    layout_id = '" . (int)$layout_id . "'");
            }
        }

        $this->cache->delete('product');

        return $product_id;
    }
 

    /**
     * Редактира продукт
     *
     * @param int $product_id ID на продукта
     * @param array $data Данни за продукта
     * @return void
     */
    public function editProduct($product_id, $data)
    {
        // Основна информация за продукта
        $this->db->query("UPDATE " . DB_PREFIX . "product SET 
            model = '" . $this->db->escape($data['model']) . "', 
            sku = '" . $this->db->escape($data['sku']) . "', 
            upc = '" . $this->db->escape($data['upc']) . "', 
            ean = '" . $this->db->escape($data['ean']) . "', 
            jan = '" . $this->db->escape($data['jan']) . "', 
            isbn = '" . $this->db->escape($data['isbn']) . "', 
            mpn = '" . $this->db->escape($data['mpn']) . "', 
            location = '" . $this->db->escape($data['location']) . "', 
            quantity = '" . (int)$data['quantity'] . "', 
            minimum = '" . (int)$data['minimum'] . "', 
            subtract = '" . (int)$data['subtract'] . "', 
            stock_status_id = '" . (int)$data['stock_status_id'] . "', 
            date_available = '" . $this->db->escape($data['date_available']) . "', 
            manufacturer_id = '" . (int)$data['manufacturer_id'] . "', 
            shipping = '" . (int)$data['shipping'] . "', 
            price = '" . (float)$data['price'] . "', 
            points = '" . (int)$data['points'] . "', 
            weight = '" . (float)$data['weight'] . "', 
            weight_class_id = '" . (int)$data['weight_class_id'] . "', 
            length = '" . (float)$data['length'] . "', 
            width = '" . (float)$data['width'] . "', 
            height = '" . (float)$data['height'] . "', 
            length_class_id = '" . (int)$data['length_class_id'] . "', 
            status = '" . (int)$data['status'] . "', 
            tax_class_id = '" . (int)$data['tax_class_id'] . "', 
            sort_order = '" . (int)$data['sort_order'] . "', 
            date_modified = NOW() 
            WHERE product_id = '" . (int)$product_id . "'");

        // Актуализиране на основното изображение, ако е зададено
        if (isset($data['image'])) {
            $this->db->query("UPDATE " . DB_PREFIX . "product 
                SET image = '" . $this->db->escape($data['image']) . "' 
                WHERE product_id = '" . (int)$product_id . "'");
        }

        // Изтриване на старите описания на продукта
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_description 
            WHERE product_id = '" . (int)$product_id . "'");

        // Добавяне на новите описания за всеки език
        foreach ($data['product_description'] as $language_id => $value) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "product_description SET 
                product_id = '" . (int)$product_id . "', 
                language_id = '" . (int)$language_id . "', 
                name = '" . $this->db->escape($value['name']) . "', 
                description = '" . $this->db->escape($value['description']) . "', 
                tag = '" . ( $value['tag'] ?? $this->db->escape($value['tag']) ) . "', 
                meta_title = '" . $this->db->escape($value['meta_title']) . "', 
                meta_description = '" . $this->db->escape($value['meta_description']) . "', 
                meta_keyword = '" . $this->db->escape($value['meta_keyword']) . "'");
        }

        // Добавяне на връзки с новите магазини
        if (isset($data['product_store'])) {

            // Изтриване на връзките с магазините
            $this->db->query("DELETE FROM " . DB_PREFIX . "product_to_store 
            WHERE product_id = '" . (int)$product_id . "'");

            foreach ($data['product_store'] as $store_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_store 
                    SET product_id = '" . (int)$product_id . "', 
                        store_id = '" . (int)$store_id . "'");
            }
        }

        // Изтриване на старите атрибути на продукта
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_attribute 
            WHERE product_id = '" . (int)$product_id . "'");

        // Добавяне на новите атрибути
        if (!empty($data['product_attribute'])) {
            foreach ($data['product_attribute'] as $product_attribute) {
                if ($product_attribute['attribute_id']) {
                    // Премахване на дублиращи се атрибути
                    $this->db->query("DELETE FROM " . DB_PREFIX . "product_attribute 
                        WHERE product_id = '" . (int)$product_id . "' 
                        AND attribute_id = '" . (int)$product_attribute['attribute_id'] . "'");

                    // Добавяне на атрибутите за всеки език
                    foreach ($product_attribute['product_attribute_description'] as $language_id => $product_attribute_description) {
                        $this->db->query("INSERT INTO " . DB_PREFIX . "product_attribute SET 
                            product_id = '" . (int)$product_id . "', 
                            attribute_id = '" . (int)$product_attribute['attribute_id'] . "', 
                            language_id = '" . (int)$language_id . "', 
                            text = '" . $this->db->escape($product_attribute_description['text']) . "'");
                    }
                }
            }
        }

        // Изтриване на старите опции на продукта
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_option 
            WHERE product_id = '" . (int)$product_id . "'");

        $this->db->query("DELETE FROM " . DB_PREFIX . "product_option_value 
            WHERE product_id = '" . (int)$product_id . "'");

        // Добавяне на новите опции
        if (isset($data['product_option'])) {
            foreach ($data['product_option'] as $product_option) {
                // Обработка на опции от тип избор (select, radio, checkbox, image)
                if (in_array($product_option['type'], ['select', 'radio', 'checkbox', 'image'])) {
                    if (isset($product_option['product_option_value'])) {
                        // Добавяне на основната опция
                        $this->db->query("INSERT INTO " . DB_PREFIX . "product_option 
                            SET product_option_id = '" . (int)$product_option['product_option_id'] . "', 
                                product_id = '" . (int)$product_id . "', 
                                option_id = '" . (int)$product_option['option_id'] . "', 
                                required = '" . (int)$product_option['required'] . "'");

                        $product_option_id = $this->db->getLastId();

                        // Добавяне на стойностите за опцията
                        foreach ($product_option['product_option_value'] as $product_option_value) {
                            $this->db->query("INSERT INTO " . DB_PREFIX . "product_option_value SET 
                                product_option_value_id = '" . (int)$product_option_value['product_option_value_id'] . "', 
                                product_option_id = '" . (int)$product_option_id . "', 
                                product_id = '" . (int)$product_id . "', 
                                option_id = '" . (int)$product_option['option_id'] . "', 
                                option_value_id = '" . (int)$product_option_value['option_value_id'] . "', 
                                quantity = '" . (int)$product_option_value['quantity'] . "', 
                                subtract = '" . (int)$product_option_value['subtract'] . "', 
                                price = '" . (float)$product_option_value['price'] . "', 
                                price_prefix = '" . $this->db->escape($product_option_value['price_prefix']) . "', 
                                points = '" . (int)$product_option_value['points'] . "', 
                                points_prefix = '" . $this->db->escape($product_option_value['points_prefix']) . "', 
                                weight = '" . (float)$product_option_value['weight'] . "', 
                                weight_prefix = '" . $this->db->escape($product_option_value['weight_prefix']) . "'");
                        }
                    }
                } else {
                    // Обработка на прости опции (текст, текстова област и др.)
                    $this->db->query("INSERT INTO " . DB_PREFIX . "product_option 
                        SET product_option_id = '" . (int)$product_option['product_option_id'] . "', 
                            product_id = '" . (int)$product_id . "', 
                            option_id = '" . (int)$product_option['option_id'] . "', 
                            value = '" . $this->db->escape($product_option['value']) . "', 
                            required = '" . (int)$product_option['required'] . "'");
                }
            }
        }

        // Изтриване на старите повтарящи се плащания
        $this->db->query("DELETE FROM `" . DB_PREFIX . "product_recurring` 
            WHERE product_id = " . (int)$product_id);

        // Добавяне на нови повтарящи се плащания
        if (isset($data['product_recurring'])) {
            foreach ($data['product_recurring'] as $product_recurring) {
                $this->db->query("INSERT INTO `" . DB_PREFIX . "product_recurring` 
                    SET `product_id` = " . (int)$product_id . ", 
                        customer_group_id = " . (int)$product_recurring['customer_group_id'] . ", 
                        `recurring_id` = " . (int)$product_recurring['recurring_id']);
            }
        }

        // Изтриване на старите отстъпки за продукта
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_discount 
            WHERE product_id = '" . (int)$product_id . "'");

        // Добавяне на нови отстъпки
        if (isset($data['product_discount'])) {
            foreach ($data['product_discount'] as $product_discount) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_discount 
                    SET product_id = '" . (int)$product_id . "', 
                        customer_group_id = '" . (int)$product_discount['customer_group_id'] . "', 
                        quantity = '" . (int)$product_discount['quantity'] . "', 
                        priority = '" . (int)$product_discount['priority'] . "', 
                        price = '" . (float)$product_discount['price'] . "', 
                        date_start = '" . $this->db->escape($product_discount['date_start']) . "', 
                        date_end = '" . $this->db->escape($product_discount['date_end']) . "'");
            }
        }



        $product_specials = isset($data['product_special']) ? $data['product_special'] : [];
        // Добавяне на нови специални цени
        $this->prepareSpecialData($product_id, $product_specials);

        // Изтриване на старите изображения на продукта
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_image 
            WHERE product_id = '" . (int)$product_id . "'");

        // Добавяне на нови изображения
        if (isset($data['product_image'])) {
            foreach ($data['product_image'] as $product_image) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_image 
                    SET product_id = '" . (int)$product_id . "', 
                        image = '" . $this->db->escape($product_image['image']) . "', 
                        sort_order = '" . (int)$product_image['sort_order'] . "'");
            }
        }

        // Добавяне на нови файлове за изтегляне
        if (isset($data['product_download'])) {
            // Изтриване на старите файлове за изтегляне
            $this->db->query("DELETE FROM " . DB_PREFIX . "product_to_download 
            WHERE product_id = '" . (int)$product_id . "'");

            foreach ($data['product_download'] as $download_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_download 
                    SET product_id = '" . (int)$product_id . "', 
                        download_id = '" . (int)$download_id . "'");
            }
        }

        // Изтриване на старите категории на продукта
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_to_category 
            WHERE product_id = '" . (int)$product_id . "'");

        // Добавяне на продукта към нови категории
        if (isset($data['product_category'])) {
            foreach ($data['product_category'] as $category_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_category 
                    SET product_id = '" . (int)$product_id . "', 
                        category_id = '" . (int)$category_id . "'");
            }
        }


        // Добавяне на нови филтри
        if (isset($data['product_filter'])) {
            // Изтриване на старите филтри на продукта
            $this->db->query("DELETE FROM " . DB_PREFIX . "product_filter 
            WHERE product_id = '" . (int)$product_id . "'");
            
            foreach ($data['product_filter'] as $filter_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_filter 
                    SET product_id = '" . (int)$product_id . "', 
                        filter_id = '" . (int)$filter_id . "'");
            }
        }

        // Изтриване на старите свързани продукти (в двете посоки)
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_related 
            WHERE product_id = '" . (int)$product_id . "' 
            OR related_id = '" . (int)$product_id . "'");

        // Добавяне на нови свързани продукти (в двете посоки)
        if (isset($data['product_related'])) {
            foreach ($data['product_related'] as $related_id) {
                // Изтриване на съществуващи връзки, за да избегнем дублиране
                $this->db->query("DELETE FROM " . DB_PREFIX . "product_related 
                    WHERE product_id = '" . (int)$product_id . "' 
                    AND related_id = '" . (int)$related_id . "'");
                
                // Добавяне на нова връзка от продукт към свързан
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_related 
                    SET product_id = '" . (int)$product_id . "', 
                        related_id = '" . (int)$related_id . "'");
                
                // Изтриване на обратната връзка, ако съществува
                $this->db->query("DELETE FROM " . DB_PREFIX . "product_related 
                    WHERE product_id = '" . (int)$related_id . "' 
                    AND related_id = '" . (int)$product_id . "'");
                
                // Добавяне на нова обратна връзка
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_related
                    SET product_id = '" . (int)$related_id . "',
                        related_id = '" . (int)$product_id . "'");
            }
        }

        // Обработка на допълнителни продукти
        if (isset($data['product_more'])) {
            $this->processAdditionalProducts($product_id, $data['product_more']);
        }

        // Добавяне на нови награди
        if (isset($data['product_reward'])) {
            // Изтриване на старите награди за продукта
            $this->db->query("DELETE FROM " . DB_PREFIX . "product_reward 
            WHERE product_id = '" . (int)$product_id . "'");
            foreach ($data['product_reward'] as $customer_group_id => $value) {
                if ((int)$value['points'] > 0) {
                    $this->db->query("INSERT INTO " . DB_PREFIX . "product_reward 
                        SET product_id = '" . (int)$product_id . "', 
                            customer_group_id = '" . (int)$customer_group_id . "', 
                            points = '" . (int)$value['points'] . "'");
                }
            }
        }

        // Изтриване на старите SEO URL адреси за продукта
        $this->db->query("DELETE FROM " . DB_PREFIX . "seo_url 
            WHERE query = 'product_id=" . (int)$product_id . "'");

        // Добавяне на нови SEO URL адреси
        if (isset($data['product_seo_url'])) {
            foreach ($data['product_seo_url'] as $store_id => $language) {
                foreach ($language as $language_id => $keyword) {
                    if (trim($keyword)) {
                        $this->db->query("INSERT INTO " . DB_PREFIX . "seo_url 
                            SET store_id = '" . (int)$store_id . "', 
                                language_id = '" . (int)$language_id . "', 
                                query = 'product_id=" . (int)$product_id . "', 
                                keyword = '" . $this->db->escape($keyword) . "'");
                    }
                }
            }
        }

        if (isset($data['product_layout'])) {

            $this->db->query("DELETE FROM " . DB_PREFIX . "product_to_layout 
            WHERE product_id = '" . (int)$product_id . "'");

            foreach ($data['product_layout'] as $store_id => $layout_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_layout 
                    SET product_id = '" . (int)$product_id . "', 
                        store_id = '" . (int)$store_id . "', 
                        layout_id = '" . (int)$layout_id . "'");
            }
        }

        $this->cache->delete('product');
    }

    private function prepareSpecialData($product_id, $product_specials) {
        // Изтриване на старите специални цени
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_special 
            WHERE product_id = '" . (int)$product_id . "'");

        // Добавяне на нови специални цени
        if ($product_specials) {
            foreach ($product_specials as $product_special) {

                $customer_group_id = isset($product_special['customer_group_id']) ? (int)$product_special['customer_group_id'] : 1;
                $priority = isset($product_special['priority']) ? (int)$product_special['priority'] : 1;

                $date_start = $product_special['date_start'] !== '0000-00-00' && $product_special['date_start'] !== '' ? $this->db->escape(date('Y-m-d', strtotime($product_special['date_start']))) : '0000-00-00';
                $date_end = $product_special['date_end'] !== '0000-00-00' && $product_special['date_end'] !== '' ? $this->db->escape(date('Y-m-d', strtotime($product_special['date_end']))) : '0000-00-00';

                $this->db->query("INSERT INTO " . DB_PREFIX . "product_special 
                    SET product_id = '" . (int)$product_id . "', 
                        customer_group_id = '" . $customer_group_id . "', 
                        priority = '" . $priority . "', 
                        price = '" . (float)$product_special['price'] . "', 
                        date_start = '" . $date_start . "', 
                        date_end = '" . $date_end . "'");
            }
        }
    }

    /**
     * Дублиране на продукт
     */
    public function duplicateProduct($product_id) {
        $query = $this->db->query("SELECT DISTINCT * FROM " . DB_PREFIX . "product p WHERE p.product_id = '" . (int)$product_id . "'");


		if ($query->num_rows) {
			$data = $query->row;
			$data['sku'] = '';
			$data['upc'] = '';
			$data['viewed'] = '0';
			$data['keyword'] = '';
			$data['status'] = '0';
            $data['image'] = $query->row['image'];
            $data['product_attribute'] = $this->getProductAttributes($product_id);
			$data['product_description'] = $this->getProductDescriptions($product_id);
			$data['product_discount'] = $this->getProductDiscounts($product_id);
			$data['product_filter'] = $this->getProductFilters($product_id);
			$data['product_image'] = $this->getProductImages($product_id);
			$data['product_option'] = $this->getProductOptions($product_id);
			$data['product_related'] = $this->getProductRelated($product_id);
            $data['product_more'] = $this->getAdditionalProducts($product_id);
			$data['product_reward'] = $this->getProductRewards($product_id);
			$data['product_special'] = $this->getProductSpecials($product_id);
			$data['product_category'] = $this->getProductCategories($product_id);
			$data['product_download'] = $this->getProductDownloads($product_id);
			$data['product_layout'] = $this->getProductLayouts($product_id);
			$data['product_store'] = $this->getProductStores($product_id);
			$data['product_recurrings'] = $this->getRecurrings($product_id);

			return $this->addProduct($data);
		}

        return false;
    }

    /**
     * Получаване на опции за продукт
     * Връща пълни данни за опциите включително product_option_id, product_option_value_id и типа на опцията
     *
     * @param int $product_id ID на продукта
     * @return array Масив с данни за опциите
     */
    public function getProductOptions($product_id) {
        $product_option_data = [];

        // Заявка за основните опции
        $product_option_query = $this->db->query("
            SELECT po.*, o.type, od.name
            FROM " . DB_PREFIX . "product_option po
            LEFT JOIN " . DB_PREFIX . "option o ON (po.option_id = o.option_id)
            LEFT JOIN " . DB_PREFIX . "option_description od ON (o.option_id = od.option_id)
            WHERE po.product_id = '" . (int)$product_id . "'
            AND od.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ORDER BY o.sort_order
        ");

        foreach ($product_option_query->rows as $product_option) {
            $product_option_value_data = [];

            // За опции с избор (select, radio, checkbox, image) зареждаме стойностите
            if (in_array($product_option['type'], ['select', 'radio', 'checkbox', 'image'])) {
                $product_option_value_query = $this->db->query("
                    SELECT pov.*, ovd.name
                    FROM " . DB_PREFIX . "product_option_value pov
                    LEFT JOIN " . DB_PREFIX . "option_value ov ON (pov.option_value_id = ov.option_value_id)
                    LEFT JOIN " . DB_PREFIX . "option_value_description ovd ON (ov.option_value_id = ovd.option_value_id)
                    WHERE pov.product_option_id = '" . (int)$product_option['product_option_id'] . "'
                    AND ovd.language_id = '" . (int)$this->config->get('config_language_id') . "'
                    ORDER BY ov.sort_order ASC
                ");

                foreach ($product_option_value_query->rows as $product_option_value) {
                    $product_option_value_data[] = [
                        'product_option_value_id' => $product_option_value['product_option_value_id'],
                        'option_value_id'         => $product_option_value['option_value_id'],
                        'quantity'                => $product_option_value['quantity'],
                        'subtract'                => $product_option_value['subtract'],
                        'price'                   => $product_option_value['price'],
                        'price_prefix'            => $product_option_value['price_prefix'],
                        'points'                  => $product_option_value['points'],
                        'points_prefix'           => $product_option_value['points_prefix'],
                        'weight'                  => $product_option_value['weight'],
                        'weight_prefix'           => $product_option_value['weight_prefix'],
                        'name'                    => $product_option_value['name']
                    ];
                }
            }

            $product_option_data[] = [
                'product_option_id'    => $product_option['product_option_id'],
                'option_id'            => $product_option['option_id'],
                'name'                 => $product_option['name'],
                'type'                 => $product_option['type'],
                'value'                => $product_option['value'] ?? '',
                'required'             => $product_option['required'],
                'product_option_value' => $product_option_value_data
            ];
        }

        return $product_option_data;
    }

    /**
     * Получаване на допълнителни продукти за даден продукт
     *
     * @param int $product_id ID на продукта
     * @return array Масив с ID-та на допълнителните продукти
     */
    public function getAdditionalProducts($product_id) {
        $additional_products = [];

        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "product_more WHERE product_id = '" . (int)$product_id . "'");

        foreach ($query->rows as $result) {
            $additional_products[] = $result['more_id'];
        }

        return $additional_products;
    }

    /**
     * Обработка на допълнителни продукти за даден продукт
     * Изтрива всички стари записи и добавя новите
     *
     * @param int $product_id ID на основния продукт
     * @param array $additional_products Масив с ID-та на допълнителните продукти
     */
    public function processAdditionalProducts($product_id, $additional_products) {
        // Изтриване на всички стари записи за този продукт
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_more WHERE product_id = '" . (int)$product_id . "'");

        // Добавяне на новите допълнителни продукти
        if (!empty($additional_products)) {
            foreach ($additional_products as $more_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_more SET
                    product_id = '" . (int)$product_id . "',
                    more_id = '" . (int)$more_id . "'");
            }
        }
    }

    /**
     * Търсене в продукти с case-insensitive заявка
     *
     * @param string $searchTerm Търсен термин
     * @param int $limit Максимален брой резултати
     * @param int $offset Отместване за пагинация
     * @return array Масив с резултати
     */
    public function searchProducts($searchTerm, $limit = 10, $offset = 0) {
        $sql = "SELECT p.product_id as id, pd.name as name
                FROM " . DB_PREFIX . "product p
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
                AND p.status = '1'
                AND LOWER(pd.name) LIKE '%" . $this->db->escape(mb_strtolower($searchTerm)) . "%'
                ORDER BY pd.name ASC
                LIMIT " . (int)$limit . " OFFSET " . (int)$offset;

        $query = $this->db->query($sql);

        return $query->rows;
    }

    /**
     * Броене на общия брой продукти за търсен термин
     *
     * @param string $searchTerm Търсен термин
     * @return int Общ брой резултати
     */
    public function countSearchProducts($searchTerm) {
        $sql = "SELECT COUNT(*) as total
                FROM " . DB_PREFIX . "product p
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
                AND p.status = '1'
                AND LOWER(pd.name) LIKE '%" . $this->db->escape(mb_strtolower($searchTerm)) . "%'";

        $query = $this->db->query($sql);

        return (int)$query->row['total'];
    }
}
