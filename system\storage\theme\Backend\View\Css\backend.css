/* Основни стилове за административния панел */
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
  font-family: 'Exo 2', sans-serif;
  background-color: #ffffff;
  font-size: 20px;
}
.search-input:focus {
  box-shadow: 0 0 0 2px rgba(110, 65, 180, 0.2);
}
.card-stats {
  transition: all 0.3s ease;
}
.card-stats:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Странична лента */
.sidebar-item:hover {
    background-color: rgba(110, 65, 180, 0.1);
}

.sidebar-item.active {
    background-color: rgba(110, 65, 180, 0.15);
    border-left: 3px solid #9000a7;
}

/* Субменюта */
.submenu {
    transition: all 0.3s ease;
    overflow: hidden;
}

.submenu.hidden {
    max-height: 0;
    opacity: 0;
}

.submenu:not(.hidden) {
    max-height: 500px;
    opacity: 1;
}

.sidebar-subitem {
    transition: all 0.2s ease;
    border-left: 2px solid transparent;
    margin-left: 1rem;
}

.sidebar-subitem:hover {
    background-color: rgba(110, 65, 180, 0.08);
    border-left-color: rgba(110, 65, 180, 0.3);
}

.sidebar-subitem.active {
    background-color: rgba(110, 65, 180, 0.12);
    border-left-color: #9000a7;
    font-weight: 500;
}

.submenu-arrow {
    transition: transform 0.2s ease;
}

.submenu-toggle {
    cursor: pointer;
}

/* Табове */
.tab-button {
    position: relative;
}

.tab-button.active {
    color: #9000a7;
    font-weight: 500;
}

.tab-button.active::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #9000a7;
}

/* Икони */
:where([class^="ri-"])::before {
    content: "\f3c2";
}

/* Чекбокс стилове */
input[type="checkbox"] {
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    position: relative;
}

input[type="checkbox"]:checked {
    background-color: #9000a7;
    border-color: #9000a7;
}

input[type="checkbox"]:checked::after {
    content: "";
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Статус баджове */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-new {
    background-color: #e0f2fe;
    color: #0369a1;
}

.status-processing {
    background-color: #fef3c7;
    color: #92400e;
}

.status-completed {
    background-color: #dcfce7;
    color: #166534;
}

.status-cancelled {
    background-color: #fee2e2;
    color: #b91c1c;
}

.status-sent {
    background-color: #e0f2fe;
    color: #0369a1;
}

.status-rejected {
    background-color: #fee2e2;
    color: #b91c1c;
}

.status-failed {
    background-color: #fef2f2;
    color: #991b1b;
}

.status-returned-payment {
    background-color: #fef3c7;
    color: #92400e;
}

.status-returned {
    background-color: #f3e8ff;
    color: #7c3aed;
}

.status-processed {
    background-color: #dcfce7;
    color: #166534;
}

.status-reset {
    background-color: #f1f5f9;
    color: #475569;
}

.status-card-payed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-card-rejected {
    background-color: #fecaca;
    color: #dc2626;
}

.status-delayed {
    background-color: #fef3c7;
    color: #d97706;
}

/* Превключвател (toggle switch) */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #9000a7;
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* Селект полета */
.custom-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236B7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
}

/* Карти със статистики */

/* Модални прозорци */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 50;
    overflow-y: auto;
}

.modal-content {
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Селектор за дата */
.date-picker::-webkit-calendar-picker-indicator {
    filter: invert(0.5);
}

/* Звездна оценка */
.star-rating {
    display: inline-flex;
}

.star-rating input {
    display: none;
}

.star-rating label {
    color: #ddd;
    cursor: pointer;
    font-size: 24px;
    padding: 0 2px;
    transition: color 0.2s;
}

.star-rating input:checked ~ label {
    color: #ffb800;
}

.star-rating label:hover,
.star-rating label:hover ~ label {
    color: #ffb800;
}

.star-display {
    color: #ddd;
}

.star-display.active {
    color: #ffb800;
}

.review-row:hover {
    background-color: rgba(110, 65, 180, 0.05);
}

/* Пагинация */
.pagination-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    transition: all 0.2s;
}

.pagination-button.active {
    background-color: #9000a7;
    color: white;
}

.pagination-button:not(.active):hover {
    background-color: rgba(110, 65, 180, 0.1);
}

/* Радио бутони */
.radio-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    cursor: pointer;
}

.radio-container input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.radio-checkmark {
    position: relative;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.radio-container:hover input ~ .radio-checkmark {
    border-color: #9000a7;
}

.radio-container input:checked ~ .radio-checkmark {
    background-color: #fff;
    border-color: #9000a7;
}

.radio-checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.radio-container input:checked ~ .radio-checkmark:after {
    display: block;
}

.radio-container .radio-checkmark:after {
    top: 3px;
    left: 3px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #9000a7;
}


input[type="checkbox"] {
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    position: relative;
    }
    input[type="checkbox"]:checked {
    background-color: #9000a7;
    border-color: #9000a7;
    }
    input[type="checkbox"]:checked::after {
    content: "";
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    }
    .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    }
    .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    }
    .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
    }
    .toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    }
    input:checked + .toggle-slider {
    background-color: #9000a7;
    }
    input:checked + .toggle-slider:before {
    transform: translateX(20px);
    }
    .custom-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236B7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
    }
    .color-picker {
    appearance: none;
    width: 2.5rem;
    height: 2.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    }
    .color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
    }
    .color-picker::-webkit-color-swatch {
    border: none;
    border-radius: 8px;
    }
    .theme-preview {
    transition: all 0.3s ease;
    }
    .font-preview {
    transition: font-family 0.3s ease;
    }
    .slider {
    appearance: none;
    width: 100%;
    height: 6px;
    border-radius: 5px;
    background: #e5e7eb;
    outline: none;
    }
    .slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #9000a7;
    cursor: pointer;
    }
    .slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #9000a7;
    cursor: pointer;
    }
    .tab-button.active {
    color: #9000a7;
    border-bottom: 2px solid #9000a7;
    }
    .theme-option.selected {
    border-color: #9000a7;
    box-shadow: 0 0 0 2px rgba(110, 65, 180, 0.2);
    }
    .font-option.selected {
    border-color: #9000a7;
    background-color: rgba(110, 65, 180, 0.1);
    }
    .button-style.selected {
    border-color: #9000a7;
    box-shadow: 0 0 0 2px rgba(110, 65, 180, 0.2);
    }
    .color-palette-item {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    }
    .color-palette-item.selected {
    outline: 2px solid #9000a7;
    outline-offset: 2px;
    }
    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
    }
    input[type="number"] {
    -moz-appearance: textfield;
    }
    .segmented-control {
    display: inline-flex;
    background-color: #f3f4f6;
    border-radius: 9999px;
    padding: 0.25rem;
    }
    .segmented-control-option {
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    }
    .segmented-control-option.active {
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .product-card {
    transition: all 0.3s ease;
    }
    .product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }
    .product-card .card-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
    }
    .product-card:hover .card-actions {
    opacity: 1;
    }

    #product-category-autocomplete {
        position: relative;
        width: 100%;
    }

    .autocomplete-suggestions {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        background-color: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 50;
        margin-top: 0.25rem;
    }

    .autocomplete-suggestion {
        padding: 0.5rem 1rem;
        cursor: pointer;
        transition: background-color 0.2s;
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .autocomplete-suggestion:hover {
        background-color: #f3f4f6;
    }

    .autocomplete-loading,
    .autocomplete-no-results {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        padding: 0.75rem 1rem;
        background-color: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        font-size: 0.875rem;
        color: #6b7280;
        z-index: 50;
        margin-top: 0.25rem;
        text-align: center;
    }

    .autocomplete-no-results {
        color: #6b7280;
        font-style: italic;
    }

    .autocomplete-suggestion.active {
        background-color: #e5e7eb;
    }

    /* Autocomplete container for related products */
    #product-related-autocomplete {
        position: relative;
        width: 100%;
    }

    /* Autocomplete container for additional products */
    #product-more-autocomplete {
        position: relative;
        width: 100%;
    }

    .autocomplete-container {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 50;
        margin-top: 0.25rem;
    }

/* Order Actions Dropdown стилове */

/* Featured search dropdown */
#featured-search-wrapper { position: relative; }
#featured-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 300px;
    overflow-y: auto;
    background: #fff;
    z-index: 50;
}

.order-actions-dropdown {
    position: absolute !important;
    right: 0 !important;
    top: 100% !important;
    margin-top: 0.25rem !important;
    width: 12rem !important;
    background-color: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    z-index: 9999 !important;
    display: block !important;
}

.order-actions-dropdown.hidden {
    display: none !important;
}

.order-actions-dropdown .py-1 {
    padding: 0.25rem 0 !important;
}

.order-actions-dropdown button {
    width: 100% !important;
    text-align: left !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
    color: #dc2626 !important;
    background-color: transparent !important;
    border: none !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    transition: background-color 0.2s !important;
}

.order-actions-dropdown button:hover {
    background-color: #fef2f2 !important;
}

.order-actions-dropdown button .mr-2 {
    margin-right: 0.5rem !important;
}

#status-dropdown li a,
#period-dropdown li a,
#sort-dropdown li a
{
    font-size: 0.875rem !important;
}

button[data-tab] {
    font-size: 1rem !important;
}

.tab-content .label:not(.text-white)
{
    font-size: 1rem !important;
    color: #9000a7 !important;
    font-weight: 500 !important;
}



#tab-images.drag-over .image-upload-area {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
    transform: scale(1.02);
}

/* --- Секция за Drag & Drop на Категории (Директна Манипулация) --- */

/* Стил за елемента, докато се влачи */
.category-item.is-dragging {
    position: fixed; /* Важно е, за да се "отлепи" от страницата */
    z-index: 1000;
    cursor: grabbing;
    border: 2px dashed #9000a7 !important; /* Пунктираната рамка, която поиска */
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transform: scale(1.02); /* Леко го уголемяваме за по-добър ефект */
    transition: none; /* Премахваме transition по време на влачене */
}

/* Placeholder, който заема мястото на влачения елемент */
#category-drag-placeholder {
    background-color: rgba(110, 65, 180, 0.05);
    border: 2px dashed rgba(110, 65, 180, 0.2);
    border-radius: 12px;
    box-sizing: border-box;
    margin: 4px 0;
}

/* --- Общи стилове за структурата на категориите (подобрени) --- */

.category-item {
    margin: 4px 0;
    border-radius: 12px;
    background-color: #fff;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    transition: box-shadow 0.2s, transform 0.2s;
}
.category-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
}

.category-inner-wrapper {
    display: flex;
    align-items: center;
    padding: 8px;
    transition: background-color 0.2s;
}

.drag-handle {
    cursor: grab;
    padding: 8px;
    color: #9ca3af;
}
.drag-handle:hover {
    color: #9000a7;
}

.expand-handle {
    width: 28px;
    text-align: center;
}

.category-expand-btn {
    width: 28px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    border-radius: 50%;
}
.category-expand-btn:hover {
    background-color: #f3f4f6;
}
.category-expand-btn i {
    transition: transform 0.2s ease-in-out;
}
.category-expand-btn[data-expanded="true"] i {
    transform: rotate(90deg);
}


.category-content {
    flex-grow: 1;
    padding: 0 12px;
}
.category-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1rem;
}
.category-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 4px;
}
.category-meta .link {
    color: #9000a7;
    text-decoration: none;
}
.category-meta .link:hover {
    text-decoration: underline;
}

.category-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    padding-right: 8px;
}

.action-btn, .action-btn-danger {
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
    color: #6b7280;
}
.action-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}
.action-btn-danger {
    color: #ef4444;
}
.action-btn-danger:hover {
    background-color: #fee2e2;
}

/* Контейнер за подкатегории */
.subcategories-container {
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
    padding-left: 15px; /* За визуално разделяне */
    margin-left: 20px;
    margin-top: 0!important;
    position: relative;
}
.subcategory-item .category-inner-wrapper {
    margin-left: 0 !important; /* Нулираме инлайн стила, защото го управляваме от контейнера */
}


/* Overlay за зареждане */
#category-move-loading {
    position: fixed;
    inset: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

#category-move-loading .loading-content {
    display: flex;
    align-items: center;
    gap: 12px;
    background-color: #fff;
    padding: 16px 24px;
    border-radius: 12px;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    font-size: 1rem;
    color: #374151;
}


/* Анимации */
@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

.image-upload-area {
    transition: all 0.2s ease-in-out;
}

/* Стилове за мениджъра на изображения */
.bg-secondary {
    background-color: #6b7280;
}

.bg-secondary:hover {
    background-color: #4b5563;
}

/* Responsive стилове за бутоните */
@media (max-width: 640px) {
    #tab-images .flex.items-center.space-x-4 {
        flex-direction: column;
        space-x: 0;
        gap: 0.75rem;
    }
}

/* Drag and Drop Styles */
.is-drag-container {
    position: relative;
}

.category-item.is-dragging {
    position: absolute !important;
    z-index: 1000;
    opacity: 0.95;
    box-shadow: 0 15px 25px rgba(0,0,0,0.15);
    transform: scale(1.01);
    cursor: grabbing !important;
    pointer-events: none; /* Предотвратява засичането на събития от влачения елемент */
}

#category-drag-placeholder {
    background-color: #f0f9ff;
    border: 2px dashed #38bdf8;
    border-radius: 0.75rem; /* .rounded-xl */
    box-sizing: border-box;
    visibility: visible;
}

.invalid-feedback {
    color: #dc2626;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.theme-pagination button[disabled] {
    display: none;
}

/* Responsive дизайн за групи потребители */
.user-groups-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

/* При ширина под 1600px - максимум 2 карти на ред */
@media (max-width: 1600px) {
    .user-groups-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* При ширина под 1500px - 1 карта на ред */
@media (max-width: 1500px) {
    .user-groups-grid {
        grid-template-columns: 1fr;
    }
}

/* Стилове за карти на групи */
.user-group-card {
    background: white;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.user-group-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.user-group-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.user-group-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
}

.user-group-card-actions {
    display: flex;
    gap: 0.5rem;
}

.user-group-card-description {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.user-group-card-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #f3f4f6;
}

.user-group-card-stat {
    text-align: center;
}

.user-group-card-stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.user-group-card-stat-label {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Стилове за Featured Products drag & drop */
#featured-selected {
    position: relative;
}

.featured-selected-item.is-dragging {
    position: absolute !important;
    z-index: 1000;
    opacity: 0.95;
    box-shadow: 0 15px 25px rgba(0,0,0,0.15);
    transform: scale(1.01);
    transition: none;
    pointer-events: none;
    cursor: grabbing !important;
}

.drag-placeholder {
    background-color: #f0f9ff;
    border: 2px dashed #38bdf8;
    border-radius: 0.5rem;
    box-sizing: border-box;
    visibility: visible;
}

/* Стилове за предложени продукти */
.featured-selected-item {
    transition: all 0.2s ease;
}

.featured-selected-item:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

.featured-selected-item .product-name-link {
    color: #2563eb;
    text-decoration: none;
    transition: color 0.2s ease;
}

.featured-selected-item .product-name-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.featured-selected-item .product-price {
    color: #059669;
    font-weight: 600;
    font-size: 0.875rem;
}

.featured-selected-item .product-price.no-price {
    color: #9ca3af;
    font-weight: 400;
}

.featured-selected-item .product-model {
    color: #6b7280;
    font-size: 0.75rem;
}

.featured-selected-item .drag-handle {
    color: #9ca3af;
    cursor: move;
    user-select: none;
}

.featured-selected-item .drag-handle:hover {
    color: #6b7280;
}