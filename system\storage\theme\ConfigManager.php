<?php

namespace Theme25;

/**
 * Клас за управление на конфигурациите за различните бази данни
 */
class ConfigManager {
    /**
     * Конфигурация за първата (основната) база данни
     *
     * @var array
     */
    private static $firstDbConfig = [];

    /**
     * Конфигурация за втората база данни
     *
     * @var array
     */
    private static $secondDbConfig = [];

    /**
     * Флаг, указващ дали конфигурацията за първата база данни е заредена
     *
     * @var bool
     */
    private static $firstDbConfigLoaded = false;

    /**
     * Флаг, указващ дали конфигурацията за втората база данни е заредена
     *
     * @var bool
     */
    private static $secondDbConfigLoaded = false;

    /**
     * Флаг, указващ дали в момента се използва втората база данни
     *
     * @var bool
     */
    private static $usingSecondDb = false;

    /**
     * Зарежда конфигурацията за първата (основната) база данни
     *
     * @param \Registry $registry Регистър с обекти
     * @return void
     */
    public static function loadFirstDbConfig($registry) {
        if (self::$firstDbConfigLoaded) {
            return;
        }

        F()->log->developer('loadFirstDbConfig', __FILE__, __LINE__);

        // Запазване на текущата конфигурация
        $config = $registry->get('config');
        // Зареждане на конфигурацията от втората база данни
        self::loadConfigFromDatabase($registry);
        self::$firstDbConfig = self::getConfigData($config);  
        self::$firstDbConfigLoaded = true;
    }

    /**
     * Зарежда конфигурацията за втората база данни
     *
     * @param \Registry $registry Регистър с обекти
     * @param \DB $secondDb Обект за връзка с втората база данни
     * @return void
     */
    public static function loadSecondDbConfig($registry, $secondDb) {
        if (self::$secondDbConfigLoaded) {
            return;
        }

        F()->log->developer('loadSecondDbConfig', __FILE__, __LINE__);

        // Запазване на текущата конфигурация
        $config = $registry->get('config');
        $originalConfig = self::getConfigData($config);

        // Зареждане на конфигурацията от втората база данни
        $db = $registry->get('db');
        $originalDb = $db;

        // Временно превключване към втората база данни
        $registry->set('db', $secondDb);

        try {
            // Зареждане на конфигурацията от втората база данни
            self::loadConfigFromDatabase($registry);

            // Запазване на конфигурацията за втората база данни
            self::$secondDbConfig = self::getConfigData($config);

            // Възстановяване на оригиналната конфигурация
            self::setConfigData($config, $originalConfig);
        } catch (\Exception $e) {
            // Записване на грешката в лог файла
            error_log($e->getMessage(), 3, 'config_manager_error.log');

            // Възстановяване на оригиналната конфигурация
            self::setConfigData($config, $originalConfig);
        } finally {
            // Възстановяване на оригиналната база данни
            $registry->set('db', $originalDb);
        }

        self::$secondDbConfigLoaded = true;
    }

    /**
     * Превключва към конфигурацията за първата (основната) база данни
     *
     * @param \Registry $registry Регистър с обекти
     * @return void
     */
    public static function switchToFirstDbConfig($registry) {
        // if (self::$usingSecondDb) {
            $config = $registry->get('config');
            self::setConfigData($config, self::$firstDbConfig);
            self::$usingSecondDb = false;
        // }
    }

    /**
     * Превключва към конфигурацията за втората база данни
     *
     * @param \Registry $registry Регистър с обекти
     * @param \DB $secondDb Обект за връзка с втората база данни
     * @return void
     */
    public static function switchToSecondDbConfig($registry, $secondDb) {
        if (!self::$usingSecondDb) {
            // Проверка дали втората база данни е инициализирана
            if ($secondDb === null) {
                return;
            }

            // Зареждане на конфигурацията за втората база данни, ако още не е заредена
            if (!self::$secondDbConfigLoaded) {
                self::loadSecondDbConfig($registry, $secondDb);
            }

            $config = $registry->get('config');
            self::setConfigData($config, self::$secondDbConfig);
            self::$usingSecondDb = true;
        }
    }

    /**
     * Зарежда конфигурацията от базата данни
     *
     * @param \Registry $registry Регистър с обекти
     * @return void
     */
    private static function loadConfigFromDatabase($registry) {
        $config = $registry->get('config');
        $db = $registry->get('db');
        $store_id = 0;
        $is_second_db = $db instanceof \Theme25\SecondDB;

        // Определяне на префикса на таблиците
        $prefix = defined('DB_PREFIX') ? DB_PREFIX : 'oc_';

        // Проверка дали базата данни е втората база данни
        if ($is_second_db) {
            // Използване на префикса за втората база данни от .env файла
            $envPath = defined('DIR_THEME') ? DIR_THEME . '.env' : __DIR__ . '/.env';
            $envLoader = new \Theme25\EnvLoader($envPath);
            $prefix = $envLoader->get('SECOND_DB_PREFIX', $prefix);
        }

        try {
            // Зареждане на настройките от базата данни
            $query = $db->query("SELECT * FROM " . $prefix . "setting WHERE store_id = '" . (int)$store_id . "' OR store_id = 0 ORDER BY store_id ASC");

            foreach ($query->rows as $setting) {
                if (!$setting['serialized']) {
                    $config->set($setting['key'], $setting['value']);
                } else {
                    $config->set($setting['key'], json_decode($setting['value'], true));
                }
            }

            $query = $db->query("SELECT * FROM `" . DB_PREFIX . "language` WHERE code = 'bg-bg'"); // за момента твърдо български
        
            if ($query->num_rows) {
                $config->set('config_language_id', $query->row['language_id']);
            }

        } catch (\Exception $e) {
            // Записване на грешката в лог файла
            if (class_exists('\Log')) {
                $log = new \Log('config_manager_error.log');
                $log->write($e->getMessage());
            }
        }

        // if ($is_second_db) $config->set('config_language_id', 2);
        // else $config->set('config_language_id', 1);
    }

    /**
     * Извлича данните от конфигурацията
     *
     * @param \Config $config Обект за конфигурация
     * @return array Данни от конфигурацията
     */
    private static function getConfigData($config) {
        $data = [];

        // Извличане на всички данни от конфигурацията
        $reflection = new \ReflectionClass($config);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $data = $property->getValue($config);

        return $data;
    }

    /**
     * Задава данните в конфигурацията
     *
     * @param \Config $config Обект за конфигурация
     * @param array $data Данни за конфигурацията
     * @return void
     */
    private static function setConfigData($config, $data) {
        // Задаване на всички данни в конфигурацията
        $reflection = new \ReflectionClass($config);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($config, $data);
    }
}
