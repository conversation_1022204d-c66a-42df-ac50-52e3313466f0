<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Featured extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    public function execute() {
        $this->setTitle('Предложени продукти');
        $this->initAdminData();
        $this->addBackendScriptWithVersion('product-featured.js', 'footer');

        // Зареждаме глобалния модел за предложени продукти
        $this->loadModelAs('catalog/product/featured', 'featuredModel');

        // Създаваме таблицата ако не съществува
        $this->featuredModel->createTable();

        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/product_featured');
    }

    private function prepareData() {
        $selected = $this->featuredModel->getFeaturedProducts();

        // Зареждаме имената/основните полета за визуализация
        $this->loadModelAs('catalog/product', 'productModel');

        $products = [];
        foreach ($selected as $row) {
            $p = $this->productModel->getProduct((int)$row['product_id']);
            if (!$p) continue;

            // Подготвяне на thumbnail изображение
            $thumb = '';
            if (!empty($p['image'])) {
                $this->loadModelAs('tool/Imageservice', 'imageService');
                try {
                    $img = $this->imageService->getImageDetailsByPath($p['image'], 48, 48);
                    $thumb = isset($img['resized_image_url']) ? $img['resized_image_url'] : '';
                } catch (\Exception $e) {
                    // игнорираме проблеми с изображенията
                }
            } else {
                $this->loadModelAs('tool/image', 'imageModel');
                try { $thumb = $this->imageModel->resize('no_image.png', 48, 48); } catch (\Exception $e) {}
            }

            // Форматиране на цената
            $price_formatted = '';
            if (isset($p['price']) && $p['price'] !== null && $p['price'] !== '') {
                $price_formatted = $this->formatCurrency($p['price']);
            }

            // Генериране на линк за редактиране на продукта
            $edit_link = $this->getAdminLink('catalog/product/edit', 'product_id='.(int)$p['product_id']);

            $products[] = [
                'product_id' => (int)$p['product_id'],
                'name' => $p['name'],
                'model' => isset($p['model']) ? $p['model'] : '',
                'price' => isset($p['price']) ? $p['price'] : 0,
                'price_formatted' => $price_formatted,
                'edit_link' => $edit_link,
                'thumb' => $thumb,
            ];
        }

        $this->setData([
            'selected_products' => $products,
            'submit_url' => $this->getAdminLink('catalog/product/featured/submit'),
            'back_url' => $this->getAdminLink('catalog/product/featured'),
            'user_token' => $this->getUserToken(),
        ]);
    }

    /**
     * AJAX: Запазва списък от точно 4 продукта като предложени
     */
    public function submit() {
        $json = ['success' => false];
        try {
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $post = $this->requestPost();
            $ids = isset($post['selected_products']) ? $post['selected_products'] : [];
            $orders = isset($post['sort_order']) ? $post['sort_order'] : [];

            // Зареждаме глобалния модел за предложени продукти
            $this->loadModelAs('catalog/product/featured', 'featuredModel');

            // Валидация на продуктите чрез модела
            $this->featuredModel->validateProducts($ids);

            // Запазване на продуктите чрез модела
            $this->featuredModel->saveFeaturedProducts($ids, $orders, $this->getUserId());

            $json['success'] = true;
            $json['message'] = 'Предложените продукти са запазени успешно.';
        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }


}

