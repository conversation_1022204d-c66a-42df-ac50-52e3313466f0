-- SQL скрипт за създаване на таблица за предложени продукти
-- Изпълнете в phpMyAdmin

CREATE TABLE IF NOT EXISTS `oc_product_featured` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `product_id` INT(11) NOT NULL,
  `sort_order` INT(11) NOT NULL DEFAULT 0,
  `date_added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` INT(11) NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_product` (`product_id`),
  KEY `idx_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
