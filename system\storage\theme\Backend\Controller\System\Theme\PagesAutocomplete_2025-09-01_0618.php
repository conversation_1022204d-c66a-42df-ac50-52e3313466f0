<?php

namespace Theme25\Backend\Controller\System\Theme;

/**
 * Суб-контролер за автодопълване на страници
 *
 * @package Theme25\Backend\Controller\System\Theme
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class PagesAutocomplete extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->loadModels();
    }

    /**
     * Зареждане на необходимите модели
     */
    private function loadModels() {
        $this->loadModelAs('catalog/information', 'informationModel');
        $this->loadModelAs('catalog/category', 'categoryModel');
        $this->loadModelAs('catalog/product', 'productModel');
    }

    /**
     * Автодопълване за страници
     */
    public function autocomplete($params) {
        $json = [];

        // Получаване на всички активни страници за dropdown в банерите
        $filter_data = [
            'filter_status' => 1, // Само активни страници
            'start' => 0,
            'limit' => 100 // Лимит за dropdown
        ];

        $pages = $this->informationModel->getInformations($filter_data);

        foreach ($pages as $page) {
            $json[] = [
                'id' => $page['information_id'],
                'name' => $page['title'],
                'url' => $this->getPageUrl($page['information_id'])
            ];
        }

        return $json;
    }

    /**
     * Генериране на URL за страница
     */
    private function getPageUrl($information_id) {
        // Генериране на frontend URL за страницата
        return $this->url->link('information/information', 'information_id=' . $information_id);
    }
}
