<?php

namespace Theme25;

/**
 * Базов клас за всички процесори
 */
abstract class BaseProcessor {

    /**
     * Регистър с обекти
     *
     * @var \Registry
     */
    protected $registry;

    /**
     * Път до контролерите
     *
     * @var string
     */
    protected $controllerPath;

    protected $templatePath;

    /**
     * Обект за зареждане на .env файла
     *
     * @var EnvLoader
     */
    protected $envLoader;

    /**
     * Обект за връзка с втората база данни (статичен, за да се инициализира само веднъж)
     *
     * @var \DB|null
     */

    protected static $firstDb = null;

    /**
     * Префикс на таблиците за първата база данни (статичен, за да се инициализира само веднъж)
     *
     * @var string
     */
    protected static $firstDbPrefix = '';



    /**
     * Обект за връзка с втората база данни (статичен, за да се инициализира само веднъж)
     *
     * @var \DB|null
     */
    protected static $secondDb = null;

    /**
     * Модули, които използват втората база данни (статични, за да се инициализират само веднъж)
     *
     * @var array
     */
    protected static $secondDbModules = [
        'startup/startup',
        'catalog/product',
        'catalog/category',
        'catalog/inquiry',
        'catalog/review',
        'catalog/manufacturer',
        'catalog/attribute',
        'catalog/option',
        'catalog/attribute_group',
        'catalog/filter',
        'catalog/download',
        'catalog/seo_url',
        'catalog/seo_url_alias',
        'catalog/seo_url_redirect',
        'catalog/seo_url_redirect_alias',
        'user/user',
        'localisation/order_status',
        'common/globalsearch',
        'report/report'
    ];

    /**
     * Модули, които трябва да използват основната база данни (изключения от $secondDbModules)
     *
     * @var array
     */
    protected static $firstDbModules = [];

    /**
     * Префикс на таблиците за втората база данни (статичен, за да се инициализира само веднъж)
     *
     * @var string
     */
    protected static $secondDbPrefix = '';

    
    /**
     * Флаг, указващ дали втората база данни е инициализирана
     *
     * @var bool
     */
    protected static $secondDbInitialized = false;

    /**
     * Конструктор
     *
     * @param \Registry $registry Регистър с обекти
     * @param string $controllerPath Път до контролерите ('Backend' или 'Frontend')
     */
    public function __construct($registry, $controllerPath = 'Frontend') {
        $this->registry = $registry;
        $this->controllerPath = $controllerPath;
        $this->templatePath = DIR_THEME . $controllerPath . '/View/Template';

        // Зареждане на .env файла
        $envPath = DIR_THEME . '.env';
        $this->envLoader = new EnvLoader($envPath);

        $is_first_db = \DataBases::getFirstDb() !== null;
        $is_second_db = $this->isSecondDatabase($registry->get('db'));

        if(!$is_first_db) {
            self::$firstDb = $registry->get('db');
            self::$firstDbPrefix = DB_PREFIX;
            \DataBases::setFirstDb(self::$firstDb);
            \DataBases::setFirstDbPrefix(self::$firstDbPrefix);
            // Инициализиране на конфигурацията за първата база данни
            \Theme25\ConfigManager::loadFirstDbConfig($registry);
        }

        // Зареждане на модулите, които трябва да използват основната база данни
        self::$firstDbModules = $this->envLoader->getArray('FIRST_DB_MODULES');

        // Инициализиране на втората база данни, ако е разрешено
        $this->initSecondDatabase();
    }

    /**
     * Инициализира връзката с втората база данни, ако е разрешено в .env файла
     * Инициализацията се извършва само веднъж за всички инстанции на класа
     *
     * @return void
     */
    protected function initSecondDatabase() {
        // Проверка дали втората база данни вече е инициализирана
        if (self::$secondDbInitialized) {
            return;
        }

        if ($this->envLoader->get('SECOND_DB_ENABLED', false)) {
            // Зареждане на модулите, които използват втората база данни
            self::$secondDbModules = array_merge(self::$secondDbModules, $this->envLoader->getArray('SECOND_DB_MODULES'));

            // Създаване на обект за връзка с втората база данни
            try {
                // Използване на стандартния DB клас на OpenCart
                $dbEngine = $this->envLoader->get('SECOND_DB_ENGINE', 'mysqli');
                $hostname = $this->envLoader->get('SECOND_DB_HOSTNAME', 'localhost');
                if (php_sapi_name() === 'cli') $hostname = '127.0.0.1';

                $username = $this->envLoader->get('SECOND_DB_USERNAME', '');
                $password = $this->envLoader->get('SECOND_DB_PASSWORD', '');
                $database = $this->envLoader->get('SECOND_DB_DATABASE', '');
                $port = $this->envLoader->get('SECOND_DB_PORT', '3306');

                // Проверка дали е включено дебъгването на втората база данни
                $debug = $this->envLoader->get('SECOND_DB_DEBUG', false);
                if ($debug) {
                    defined('DEBUG_SECOND_DB') || define('DEBUG_SECOND_DB', true);
                }

                \DataBases::setFirstDb($this->registry->get('db'));
                \DataBases::setFirstDbPrefix(DB_PREFIX);

                // Създаване на инстанция на SecondDB класа с идентификатор
                $dbIdentifier = $this->envLoader->get('SECOND_DB_IDENTIFIER', 'second_db');
                self::$secondDb = new \Theme25\SecondDB($dbEngine, $hostname, $username, $password, $database, $port, $dbIdentifier);

                // Задаване на времевата зона
                self::$secondDb->query("SET time_zone = '" . self::$secondDb->escape(date('P')) . "'");

                // Запазване на префикса на таблиците
                self::$secondDbPrefix = $this->envLoader->get('SECOND_DB_PREFIX', '');

                // Маркиране на втората база данни като инициализирана
                self::$secondDbInitialized = true;

                // Зареждане на конфигурацията за втората база данни само ако е инициализирана
                if (self::$secondDb !== null) {
                    \Theme25\ConfigManager::loadSecondDbConfig($this->registry, self::$secondDb);
                }
            } catch (\Exception $e) {
                // Записване на грешката в лог файла
                if (class_exists('\Log')) {
                    $log = new \Log('second_db_error.log');
                    $log->write($e->getMessage() . ' ' . $e->getTraceAsString());
                }
            }
        } else {
            // Маркиране на втората база данни като инициализирана, дори ако не е разрешена
            self::$secondDbInitialized = true;
        }
    }

    /**
     * Проверява дали даден път трябва да използва втората база данни
     *
     * @param string $route Път за проверка
     * @return bool Дали пътят трябва да използва втората база данни
     */
    protected function shouldUseSecondDb($route) {
        if (self::$secondDb === null) {
            return false;
        }

        // Първо проверяваме дали модулът е в списъка за основната база данни (изключения)
        foreach (self::$firstDbModules as $module) {
            if (strpos(strtolower($route), strtolower($module)) === 0) {
                return false; // Принудително използване на основната база данни
            }
        }

        // След това проверяваме дали модулът е в списъка за втората база данни
        foreach (self::$secondDbModules as $module) {
            if (strpos(strtolower($route), strtolower($module)) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Връща обекта за зареждане на .env файла
     *
     * @return EnvLoader
     */
    public function getEnvLoader() {
        return $this->envLoader;
    }



    /**
     * Връща обекта за връзка с втората база данни
     *
     * @return \DB|null
     */
    public function getFirstDatabase() {
        return self::$firstDb;
    }

    /**
     * Връща префикса на таблиците за първата база данни
     *
     * @return string
     */
    public function getFirstDatabasePrefix() {
        return self::$firstDbPrefix;
    }

    /**
     * Връща обекта за връзка с втората база данни
     *
     * @return \DB|null
     */
    public function getSecondDatabase() {
        return self::$secondDb;
    }

    /**
     * Връща префикса на таблиците за втората база данни
     *
     * @return string
     */
    public function getSecondDatabasePrefix() {
        return self::$secondDbPrefix;
    }

    /**
     * Проверява дали дадена инстанция на база данни е втората база данни
     *
     * @param \DB $db Инстанция на база данни
     * @return bool Дали инстанцията е втората база данни
     */
    public function isSecondDatabase($db) {
        if ($db instanceof \Theme25\SecondDB) {
            return true;
        }

        return $db === self::$secondDb;
    }

    /**
     * Превключва към втората база данни и зарежда съответната конфигурация
     *
     * @param \Registry $registry Регистър с обекти
     * @return void
     */
    protected function switchToSecondDatabase($registry) {
        // Проверка дали втората база данни е инициализирана
        if (self::$secondDb === null) {
            return;
        }

        // Превключване към конфигурацията за втората база данни
        \Theme25\ConfigManager::switchToSecondDbConfig($registry, self::$secondDb);
    }

    /**
     * Превключва към първата (основната) база данни и зарежда съответната конфигурация
     *
     * @param \Registry $registry Регистър с обекти
     * @return void
     */
    protected function switchToFirstDatabase($registry) {
        // Превключване към конфигурацията за първата (основната) база данни
        $registry->set('db', self::$firstDb);
        \Theme25\ConfigManager::switchToFirstDbConfig($registry);
    }

    /**
     * Връща стойност от конфигурацията, стойност по подразбиране или цялата конфигурация
     *
     * @param string|null $key Ключ на конфигурационната стойност (ако е null, връща цялата конфигурация)
     * @param mixed $default Стойност по подразбиране, ако ключът не съществува
     * @return mixed Стойността от конфигурацията, стойността по подразбиране или цялата конфигурация
     */
    protected function getConfig($key = null, $default = null) {
        if ($key === null) {
            return $this->registry->get('config');
        }

        if ($this->registry->get('config')->has($key)) {
            $value = $this->registry->get('config')->get($key);
            return $value;
        }

        return $default;
    }
}


