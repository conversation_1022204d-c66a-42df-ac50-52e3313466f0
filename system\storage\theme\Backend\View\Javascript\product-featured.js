(function(){
  'use strict';

  // Разширяваме BackendModule
  const ProductFeaturedModule = Object.create(BackendModule);

  Object.assign(ProductFeaturedModule, {
    config: {
      maxItems: 4,
      debounceMs: 300
    },

    init: function() {
      const search = document.getElementById('featured-search');
      const results = document.getElementById('featured-search-results');
      const selected = document.getElementById('featured-selected');
      const saveBtn = document.getElementById('featured-save');
      if(!search || !results || !selected || !saveBtn) return;

      this.state = {items: this.readSelected(selected)};

      // Състояние за търсене и странициране
      this.searchState = {
        query: '',
        start: 0,
        limit: 10,
        loading: false,
        hasMore: true,
        abortController: null,
        resultsEl: results
      };

      // Показвай първите 10 предложения при фокус/клик
      const showInitial = () => {
        if (search.value.trim().length === 0) {
          this.loadInitialProducts();
        } else {
          this.searchProducts(search.value.trim());
        }
      };
      search.addEventListener('focus', showInitial);
      search.addEventListener('click', showInitial);

      // Търсене с debounce при въвеждане
      let t;
      search.addEventListener('input', (e) => {
        clearTimeout(t);
        const q = e.target.value.trim();
        if(q.length === 0){
          this.loadInitialProducts();
          return;
        }
        t = setTimeout(() => this.searchProducts(q), this.config.debounceMs);
      });

      // Премахване на избран
      selected.addEventListener('click', (e) => {
        const removeBtn = e.target.closest('.remove-selected');
        if(!removeBtn) return;
        const id = parseInt(removeBtn.getAttribute('data-id'), 10);
        const container = document.getElementById('featured-selected');
        let card = null;
        if (container) { card = container.querySelector(`.featured-selected-item[data-id="${id}"]`); }
        if (!card) { card = removeBtn.closest('.featured-selected-item'); }
        if (!card) return;
        card.remove();
        this.updateStateFromDom();

        // Допълнително обновяване на брояча за сигурност
        this.updateSelectedCounter();
      });

      // Затваряне на dropdown при избор/клик извън него
      document.addEventListener('click', (ev) => {
        const wrapper = document.getElementById('featured-search-wrapper');
        if(!wrapper) return;
        const within = wrapper.contains(ev.target);
        if(!within){ results.classList.add('hidden'); }
      });

      // Инициализиране на drag & drop
      this.attachDnd(selected);

      // Infinite scroll за резултатите
      this.attachInfiniteScroll(results);

      // Запис
      saveBtn.addEventListener('click', () => this.save());

      // Стартираме с правилен брояч
      this.updateSelectedCounter();
    },

    readSelected: function(container){
      const items = [];
      container.querySelectorAll('.featured-selected-item[data-id]').forEach(el => {
        items.push({product_id: parseInt(el.getAttribute('data-id'), 10)});
      });
      return items;
    },

    updateStateFromDom: function(){
      const container = document.getElementById('featured-selected');
      const items = [];
      container.querySelectorAll('.featured-selected-item[data-id]').forEach(el => {
        items.push({product_id: parseInt(el.getAttribute('data-id'), 10)});
      });
      this.state.items = items;
      this.updateSelectedCounter();
    },

    updateSelectedCounter: function(){
      const el = document.getElementById('featured-selected-count');
      if(el){
        // Броим само картите на продукти, не и бутоните за изтриване
        const container = document.getElementById('featured-selected');
        const actualCount = container ? container.querySelectorAll('.featured-selected-item[data-id]').length : 0;
        el.textContent = String(actualCount);
      }
    },

    attachDnd: function(container){
      if(this._dndAttached) return;
      this._dndAttached = true;

      // Състояние за drag & drop
      this.dragState = {
        draggedElement: null,
        dragPlaceholder: null,
        originalIndex: null,
        mouseOffsetY: 0,
        isDragging: false
      };

      // Закачаме bound методи за да можем да ги премахваме
      this.boundOnMouseMove = this.onMouseMove.bind(this);
      this.boundOnMouseUp = this.onMouseUp.bind(this);

      // Използваме event delegation за mousedown
      container.addEventListener('mousedown', this.onMouseDown.bind(this));
    },

    // Mouse-based drag & drop методи
    onMouseDown: function(e) {
      // Проверяваме дали е натиснат drag handle
      if (!e.target.closest('.drag-handle') || e.button !== 0) return;

      e.preventDefault();
      e.stopPropagation();

      const item = e.target.closest('.featured-selected-item[data-id]');
      if (!item) return;

      this.dragState.draggedElement = item;
      this.dragState.isDragging = false;

      const container = item.parentElement;
      this.dragState.originalIndex = Array.from(container.children).indexOf(item);

      // Запазваме mouse offset
      const rect = item.getBoundingClientRect();
      this.dragState.mouseOffsetY = e.clientY - rect.top;

      // Добавяме event listeners
      document.addEventListener('mousemove', this.boundOnMouseMove);
      document.addEventListener('mouseup', this.boundOnMouseUp);
    },

    onMouseMove: function(e) {
      if (!this.dragState.draggedElement) return;

      // Започваме drag операцията при първо движение
      if (!this.dragState.isDragging) {
        this.startDrag(e);
      }

      this.updateDragPosition(e);
      this.checkAndSwapElements(e);
    },

    onMouseUp: function() {
      if (!this.dragState.draggedElement) return;

      this.endDrag();
    },

    startDrag: function(e) {
      const element = this.dragState.draggedElement;
      const rect = element.getBoundingClientRect();
      const container = element.parentElement;

      this.dragState.isDragging = true;

      // Създаваме placeholder
      this.dragState.dragPlaceholder = document.createElement('div');
      this.dragState.dragPlaceholder.className = 'drag-placeholder bg-gray-200 border-2 border-dashed border-gray-300 rounded';
      this.dragState.dragPlaceholder.style.height = rect.height + 'px';
      this.dragState.dragPlaceholder.style.margin = window.getComputedStyle(element).margin;

      element.after(this.dragState.dragPlaceholder);

      // Правим елемента absolute positioned (като в categories.js)
      element.style.position = 'absolute';
      element.style.top = `${element.offsetTop}px`;
      element.style.left = `${element.offsetLeft}px`;
      element.style.width = `${rect.width}px`;
      element.style.zIndex = '1000';
      element.classList.add('is-dragging', 'shadow-lg');

      container.classList.add('is-drag-container');

      // Обновяваме mouseOffsetY с текущата позиция на мишката спрямо елемента (като в categories.js)
      this.dragState.mouseOffsetY = e.clientY - rect.top;

      // Дебъг информация
      console.log('startDrag - mouseOffsetY:', this.dragState.mouseOffsetY, 'e.clientY:', e.clientY, 'rect.top:', rect.top);
    },

    updateDragPosition: function(e) {
      const element = this.dragState.draggedElement;
      if (!element || !this.dragState.isDragging) return;

      const parentContainer = this.dragState.dragPlaceholder.parentElement;
      if (!parentContainer) return;

      const parentRect = parentContainer.getBoundingClientRect();
      const elementRect = element.getBoundingClientRect();

      // Изчисляваме новата горна позиция спрямо родителския контейнер (точно като в categories.js)
      let newTop = e.clientY - parentRect.top - this.dragState.mouseOffsetY;

      // Ограничаваме движението в рамките на родителския контейнер
      if (newTop < 0) {
        newTop = 0;
      } else if (newTop + elementRect.height > parentRect.height) {
        newTop = parentRect.height - elementRect.height;
      }

      element.style.top = `${newTop}px`;

      // Дебъг информация
      console.log('updateDragPosition - e.clientY:', e.clientY, 'parentRect.top:', parentRect.top, 'mouseOffsetY:', this.dragState.mouseOffsetY, 'newTop:', newTop);
    },

    checkAndSwapElements: function(e) {
      if (!this.dragState.dragPlaceholder) return;

      const container = this.dragState.dragPlaceholder.parentElement;
      if (!container) return;

      const afterElement = this.getDragAfterElement(container, e.clientY);

      if (afterElement === null) {
        // Проверяваме дали placeholder все още е в контейнера преди appendChild
        if (container.contains(this.dragState.dragPlaceholder)) {
          container.appendChild(this.dragState.dragPlaceholder);
        }
      } else if (afterElement !== this.dragState.dragPlaceholder) {
        // Проверяваме дали и двата елемента са в контейнера преди insertBefore
        if (container.contains(this.dragState.dragPlaceholder) && container.contains(afterElement)) {
          container.insertBefore(this.dragState.dragPlaceholder, afterElement);
        }
      }
    },

    getDragAfterElement: function(container, y) {
      // Търсим само картите на продукти и placeholder-а, не и бутоните за изтриване
      const draggableElements = [...container.querySelectorAll('.featured-selected-item[data-id], .drag-placeholder')]
        .filter(el => el !== this.dragState.draggedElement);

      let closest = null;
      let closestOffset = Number.NEGATIVE_INFINITY;

      draggableElements.forEach(child => {
        const box = child.getBoundingClientRect();
        const offset = y - box.top - box.height / 2;
        if (offset < 0 && offset > closestOffset) {
          closestOffset = offset;
          closest = child;
        }
      });

      return closest;
    },

    endDrag: function() {
      const element = this.dragState.draggedElement;
      const placeholder = this.dragState.dragPlaceholder;

      if (element && placeholder && placeholder.parentNode) {
        // Връщаме елемента на мястото на placeholder-а
        placeholder.parentNode.insertBefore(element, placeholder);
        placeholder.remove();

        // Премахваме drag стиловете
        element.removeAttribute('style');
        element.classList.remove('is-dragging', 'shadow-lg');

        const container = element.parentElement;
        container.classList.remove('is-drag-container');

        // Проверяваме дали позицията се е променила
        const newIndex = Array.from(container.children).indexOf(element);
        if (this.dragState.originalIndex !== newIndex) {
          this.updateStateFromDom();
        }

        // Винаги обновяваме брояча след drag операция
        this.updateSelectedCounter();
      }

      // Почистваме event listeners
      document.removeEventListener('mousemove', this.boundOnMouseMove);
      document.removeEventListener('mouseup', this.boundOnMouseUp);

      // Нулираме състоянието
      this.dragState = {
        draggedElement: null,
        dragPlaceholder: null,
        originalIndex: null,
        mouseOffsetY: 0,
        isDragging: false
      };
    },

    buildAutocompleteUrl: function(params){
      // Използваме user_token от конфигурацията или fallback към BackendModule
      const userToken = (window.ProductFeaturedConfig && window.ProductFeaturedConfig.userToken)
        ? window.ProductFeaturedConfig.userToken
        : (BackendModule.config ? BackendModule.config.userToken : '');
      const ts = Date.now();
      const type = 'product';
      const limit = params.limit || this.searchState.limit;
      let url = `index.php?route=catalog/product/autocomplete&type=${type}&limit=${limit}&user_token=${userToken}&_=${ts}`;
      if (params.filter_name && params.filter_name.length > 0) {
        url += `&filter_name=${encodeURIComponent(params.filter_name)}`;
      }
      if (typeof params.start === 'number') {
        url += `&start=${params.start}`;
      }
      return url;
    },

    renderResultItem: function(item){
      const div = document.createElement('div');
      div.className = 'px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center gap-3 border-b last:border-b-0';
      div.innerHTML = `
        <img src="${item.thumb || ''}" alt="" class="w-8 h-8 rounded object-cover"/>
        <div class="flex-1">
          <div class="font-medium">${item.name}</div>
          <div class="text-xs text-gray-500">Модел: ${item.model || 'N/A'} | Цена: ${item.price || '0.00'} лв.</div>
        </div>
      `;
      div.addEventListener('click', () => {
        this.addSelected(item);
        // Затваряме dropdown-а и изчистваме полето
        const results = document.getElementById('featured-search-results');
        if(results){ results.classList.add('hidden'); }
        const input = document.getElementById('featured-search');
        if(input){ input.value=''; }
      });
      return div;
    },

    resetSearch: function(){
      this.searchState.start = 0;
      this.searchState.hasMore = true;
      this.searchState.resultsEl.innerHTML = '';
      this.searchState.resultsEl.classList.remove('hidden');
    },

    loadInitialProducts: function(){
      if (this.searchState.loading) return;
      this.searchState.query = '';
      this.resetSearch();
      this.fetchAndRenderSuggestions('');
    },

    searchProducts: function(q){
      if (this.searchState.loading) return;
      this.searchState.query = q;
      this.resetSearch();
      this.fetchAndRenderSuggestions(q);
    },

    fetchAndRenderSuggestions: function(q){
      if (this.searchState.loading || !this.searchState.hasMore) return;
      this.searchState.loading = true;
      // Abort предишна заявка
      if (this.searchState.abortController) {
        try { this.searchState.abortController.abort(); } catch(e) {}
      }
      this.searchState.abortController = new AbortController();

      const url = this.buildAutocompleteUrl({ filter_name: q, start: this.searchState.start, limit: this.searchState.limit });
      fetch(url, { headers: { 'Cache-Control': 'no-cache' }, signal: this.searchState.abortController.signal })
        .then(r => {
          if (!r.ok) throw new Error('HTTP ' + r.status);
          return r.json();
        })
        .then(json => {
          const list = Array.isArray(json) ? json : [];
          if (this.searchState.start === 0) {
            this.searchState.resultsEl.innerHTML = '';
          }
          list.forEach(item => {
            this.searchState.resultsEl.appendChild(this.renderResultItem(item));
          });
          if (list.length < this.searchState.limit) {
            this.searchState.hasMore = false;
          } else {
            this.searchState.start += this.searchState.limit;
          }
          this.searchState.resultsEl.classList.remove('hidden');
        })
        .catch(() => {
          // скриваме при грешка
          this.searchState.resultsEl.classList.add('hidden');
        })
        .finally(() => {
          this.searchState.loading = false;
        });
    },

    attachInfiniteScroll: function(resultsEl){
      resultsEl.addEventListener('scroll', () => {
        const nearBottom = resultsEl.scrollTop + resultsEl.clientHeight >= resultsEl.scrollHeight - 10;
        if (nearBottom && this.searchState.hasMore && !this.searchState.loading) {
          this.fetchAndRenderSuggestions(this.searchState.query);
        }
      });
    },


    addSelected: function(item){
      // Предпазване от дубликати – проверка по DOM (само картите на продукти)
      const container = document.getElementById('featured-selected');
      if (container && container.querySelector(`.featured-selected-item[data-id="${item.product_id}"]`)) return;

      // Проверка за максимален брой продукти преди добавяне
      const currentCount = container ? container.querySelectorAll('.featured-selected-item[data-id]').length : 0;
      if(currentCount >= this.config.maxItems) {
        this.showAlert('error', 'Може да изберете максимум 4 продукта.');
        return;
      }

      // Добавяме картата в DOM
      const div = document.createElement('div');
      div.className = 'featured-selected-item flex items-center justify-between rounded px-3 py-2 bg-gray-100 border border-gray-200';
      div.setAttribute('data-id', item.product_id);
      div.setAttribute('draggable', 'true');
      div.innerHTML = `
        <div class="flex items-center gap-3">
          <span class="drag-handle cursor-move select-none text-gray-400" title="Премести">⋮⋮</span>
          ${item.thumb ? `<img src="${item.thumb}" alt="" class="w-10 h-10 rounded object-cover"/>` : ''}
          <div class="leading-tight">
            <div class="font-medium text-gray-800">${item.name}</div>
            ${item.model ? `<div class="text-xs text-gray-500">Код: ${item.model}</div>` : ''}
          </div>
        </div>
        <button class="remove-selected w-8 h-8 flex items-center justify-center text-gray-500 hover:text-red-600" data-id="${item.product_id}" aria-label="Премахни"><span aria-hidden="true">✕</span></button>
      `;
      container.appendChild(div);

      // Синхронизираме state от DOM и обновяваме брояча
      this.updateStateFromDom();

      // Допълнително обновяване на брояча за сигурност
      this.updateSelectedCounter();
    },

    save: function(){
      if(this.state.items.length !== this.config.maxItems){
        this.showAlert('error', 'Трябва да изберете точно 4 продукта.');
        return;
      }

      const body = new URLSearchParams();
      this.state.items.forEach((i, idx) => {
        body.append('selected_products[]', i.product_id);
        body.append('sort_order[]', idx);
      });

      fetch(window.ProductFeaturedConfig.submitUrl, {
        method: 'POST',
        headers: { 'X-Requested-With': 'XMLHttpRequest', 'Content-Type': 'application/x-www-form-urlencoded' },
        body: body.toString()
      })
      .then(r => r.json())
      .then(json => {
        if(json && json.success){
          this.showAlert('success', 'Записът е успешен.');
        } else {
          this.showAlert('error', 'Грешка: ' + (json.error || 'неизвестна'));
        }
      })
      .catch(() => this.showAlert('error', 'Възникна грешка при запис.'));
    },

    // Метод за показване на съобщения
    showAlert: function(type, message) {
      // Използваме showAlert метода от BackendModule ако е наличен
      if (window.BackendModule && typeof window.BackendModule.showAlert === 'function') {
        window.BackendModule.showAlert(type, message);
      } else {
        // Fallback към alert() ако BackendModule не е наличен
        alert(message);
      }
    },
  });

  document.addEventListener('DOMContentLoaded', function(){
    if(window.BackendModule){ ProductFeaturedModule.init(); }
  });
})();

