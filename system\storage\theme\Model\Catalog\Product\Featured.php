<?php

namespace Theme25\Model\Catalog\Product;

/**
 * Модел за управление на предложени продукти
 * Глобален модел който може да се използва както от Backend, така и от Frontend
 */
class Featured extends \Theme25\Model {

    public function __construct($registry) {
        parent::__construct($registry);
        $this->db = $this->getSecondDatabase();
        $this->disableAutoSwitch();
    }

    /**
     * Получава списък с текущо предложените продукти
     *
     * @return array Масив с product_id и sort_order
     */
    public function getFeaturedProducts() {
        $table = DB_PREFIX . 'product_featured';
        try {
            $exists = $this->db->query("SHOW TABLES LIKE '" . $this->db->escape($table) . "'");
            if ($exists && $exists->num_rows > 0) {
                $res = $this->db->query("SELECT product_id, sort_order FROM `".$table."` ORDER BY sort_order ASC, id ASC");
                return $res->rows;
            }
        } catch (\Exception $e) {
            // Логване на грешката
            if (class_exists('\Log')) {
                $log = new \Log('featured_products_error.log');
                $log->write('Error in getFeaturedProducts: ' . $e->getMessage());
            }
        }
        return [];
    }

    /**
     * Създава таблицата за предложени продукти ако не съществува
     *
     * @return bool Успешно ли е създадена таблицата
     */
    public function createTable() {
        $sql = "CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "product_featured` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `product_id` INT(11) NOT NULL,
            `sort_order` INT(11) NOT NULL DEFAULT 0,
            `date_added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `created_by` INT(11) NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `uniq_product` (`product_id`),
            KEY `idx_sort` (`sort_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";
        
        try {
            $this->db->query($sql);
            return true;
        } catch (\Exception $e) {
            // Логване на грешката
            if (class_exists('\Log')) {
                $log = new \Log('featured_products_error.log');
                $log->write('Error in createTable: ' . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Запазва списък от предложени продукти
     *
     * @param array $productIds Масив с ID-та на продуктите
     * @param array $sortOrders Масив със sort_order стойности
     * @param int $userId ID на потребителя който прави промяната
     * @return bool Успешно ли е запазването
     * @throws \Exception При грешка в данните или database операциите
     */
    public function saveFeaturedProducts($productIds, $sortOrders, $userId) {
        if (!is_array($productIds) || !is_array($sortOrders)) {
            throw new \Exception('Невалидни данни за продуктите.');
        }

        // Премахване на дубликати и каст към int
        $productIds = array_map('intval', $productIds);
        $sortOrders = array_map('intval', $sortOrders);

        if (count($productIds) !== 4) {
            throw new \Exception('Трябва да изберете точно 4 продукта.');
        }

        // Изграждаме масив от [product_id => sort_order] според позицията
        $pairs = [];
        foreach ($productIds as $idx => $pid) {
            $pairs[(int)$pid] = isset($sortOrders[$idx]) ? (int)$sortOrders[$idx] : $idx;
        }

        try {
            // Започваме транзакция за атомарност
            $this->db->query("START TRANSACTION");

            // Изчистваме старите записи
            $this->db->query("TRUNCATE TABLE `" . DB_PREFIX . "product_featured`");

            // Добавяме новите записи
            foreach ($pairs as $pid => $sort) {
                $sql = "INSERT INTO `" . DB_PREFIX . "product_featured` SET 
                        product_id = '" . (int)$pid . "', 
                        sort_order = '" . (int)$sort . "', 
                        date_added = NOW(), 
                        created_by = '" . (int)$userId . "'";
                $this->db->query($sql);
            }

            // Потвърждаваме транзакцията
            $this->db->query("COMMIT");
            return true;

        } catch (\Exception $e) {
            // Отменяме транзакцията при грешка
            $this->db->query("ROLLBACK");
            
            // Логване на грешката
            if (class_exists('\Log')) {
                $log = new \Log('featured_products_error.log');
                $log->write('Error in saveFeaturedProducts: ' . $e->getMessage());
            }
            
            throw $e;
        }
    }

    /**
     * Валидира дали продуктите съществуват в базата данни
     *
     * @param array $productIds Масив с ID-та на продуктите за валидация
     * @return bool Валидни ли са всички продукти
     * @throws \Exception При невалиден продукт
     */
    public function validateProducts($productIds) {
        if (!is_array($productIds)) {
            throw new \Exception('Невалидни данни за продуктите.');
        }

        foreach ($productIds as $pid) {
            $pid = (int)$pid;
            $query = $this->db->query("SELECT product_id FROM `" . DB_PREFIX . "product` WHERE product_id = '" . $pid . "' LIMIT 1");
            
            if (!$query || $query->num_rows === 0) {
                throw new \Exception('Невалиден продукт ID: ' . $pid);
            }
        }

        return true;
    }

    /**
     * Получава броя на предложените продукти
     *
     * @return int Брой предложени продукти
     */
    public function getFeaturedProductsCount() {
        try {
            $query = $this->db->query("SELECT COUNT(*) as total FROM `" . DB_PREFIX . "product_featured`");
            return (int)$query->row['total'];
        } catch (\Exception $e) {
            // Логване на грешката
            if (class_exists('\Log')) {
                $log = new \Log('featured_products_error.log');
                $log->write('Error in getFeaturedProductsCount: ' . $e->getMessage());
            }
            return 0;
        }
    }

    /**
     * Проверява дали даден продукт е предложен
     *
     * @param int $productId ID на продукта
     * @return bool Дали продуктът е предложен
     */
    public function isProductFeatured($productId) {
        try {
            $query = $this->db->query("SELECT product_id FROM `" . DB_PREFIX . "product_featured` WHERE product_id = '" . (int)$productId . "' LIMIT 1");
            return $query && $query->num_rows > 0;
        } catch (\Exception $e) {
            // Логване на грешката
            if (class_exists('\Log')) {
                $log = new \Log('featured_products_error.log');
                $log->write('Error in isProductFeatured: ' . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Получава ID-та на предложените продукти за Frontend (с лимит)
     * Използва се в Home контролера за показване на предложени продукти
     *
     * @param int $limit Максимален брой продукти за връщане (по подразбиране 4)
     * @return array Масив с product_id стойности
     */
    public function getFeaturedProductIds($limit = 4) {
        $featuredIds = [];

        try {
            $tableName = DB_PREFIX . 'product_featured';
            $exists = $this->db->query("SHOW TABLES LIKE '" . $this->db->escape($tableName) . "'");

            if ($exists && $exists->num_rows > 0) {
                $result = $this->db->query("SELECT product_id FROM `" . $tableName . "` ORDER BY sort_order ASC, id ASC LIMIT " . (int)$limit);
                foreach ($result->rows as $row) {
                    $featuredIds[] = (int)$row['product_id'];
                }
            }
        } catch (\Exception $e) {
            // Логване на грешката
            if (class_exists('\Log')) {
                $log = new \Log('featured_products_error.log');
                $log->write('Error in getFeaturedProductIds: ' . $e->getMessage());
            }
        }

        return $featuredIds;
    }

    /**
     * Проверява дали таблицата за предложени продукти съществува и има данни
     *
     * @return bool Дали таблицата съществува и има записи
     */
    public function hasFeaturedProducts() {
        try {
            $tableName = DB_PREFIX . 'product_featured';
            $exists = $this->db->query("SHOW TABLES LIKE '" . $this->db->escape($tableName) . "'");

            if ($exists && $exists->num_rows > 0) {
                $count = $this->db->query("SELECT COUNT(*) as total FROM `" . $tableName . "`");
                return $count && (int)$count->row['total'] > 0;
            }
        } catch (\Exception $e) {
            // Логване на грешката
            if (class_exists('\Log')) {
                $log = new \Log('featured_products_error.log');
                $log->write('Error in hasFeaturedProducts: ' . $e->getMessage());
            }
        }

        return false;
    }
}
