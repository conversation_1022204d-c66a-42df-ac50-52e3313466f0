# Доработка на Header.php с функционалност за потребители и количка

## Промпт (Подканата)
Доработи файла `F:\Web\Rakla.bg - NEW\system\storage\theme\Frontend\Controller\Common\Header.php`, като добавиш следната функционалност:

1. **Проверка за логнат потребител:**
   - Добави проверка дали потребителят е логнат в системата
   - Използвай подходящия метод от OpenCart за проверка на статуса на потребителя

2. **Бутон "Профил":**
   - Ако потребителят е логнат: генерирай URL към профилната страница
   - Ако потребителят НЕ е логнат: генерирай URL към страницата за вход/регистрация
   - Предай съответния URL към шаблона

3. **Бутон "Wishlist" (Списък с желания):**
   - Ако потребителят е логнат: генерирай URL към wishlist страницата
   - Ако потребителят НЕ е логнат: генерирай URL към страницата за вход/регистрация
   - Предай съответния URL към шаблона

4. **Бутон "Количка":**
   - Провери колко продукта има в количката на потребителя
   - Предай броя продукти към шаблона за показване в интерфейса
   - Ако няма продукти, показвай "0" или подходящо съобщение

5. **Технически изисквания:**
   - Следвай съществуващия стил на кодиране в контролера
   - Използвай методите от базовия Controller клас където е възможно
   - Предай всички необходими данни към шаблона чрез `$this->data` масива
   - Спазвай принципите за разделяне на отговорностите - бизнес логиката да остане в контролера

## Извършени промени

### 1. Създаване на резервно копие
Създадено е резервно копие на оригиналния файл:
```
system\storage\theme\Frontend\Controller\Common\Header_2025-09-01_1430.php
```

### 2. Рефакториране на метода prepareUserData()
Оригиналният метод `prepareUserData()` е разширен и разделен на няколко специализирани метода за по-добра четимост и поддръжка:

#### Нови методи:
- `isCustomerLogged()` - Проверява дали потребителят е логнат
- `prepareUserUrls($isLogged)` - Подготвя URL адресите според статуса на потребителя
- `prepareCartData()` - Подготвя данните за количката

### 3. Функционалност за проверка на логнат потребител

**Метод:** `isCustomerLogged()`
```php
private function isCustomerLogged() {
    // Проверяваме дали customer обектът съществува и дали потребителят е логнат
    if (isset($this->customer) && is_object($this->customer) && method_exists($this->customer, 'isLogged')) {
        return $this->customer->isLogged();
    }
    
    return false;
}
```

**Особености:**
- Безопасна проверка на съществуването на `$this->customer` обекта
- Проверка на метода `isLogged()` преди извикването му
- Връща `false` ако обектът не съществува или няма необходимия метод

### 4. Динамично генериране на URL адреси

**Метод:** `prepareUserUrls($isLogged)`

**За логнати потребители:**
- `account_url` → `account/account` (профилна страница)
- `wishlist_url` → `account/wishlist` (списък с желания)

**За нелогнати потребители:**
- `account_url` → `account/login` (страница за вход)
- `wishlist_url` → `account/login` (пренасочване към вход)

**Винаги достъпни:**
- `cart_url` → `checkout/cart` (количка)

**Допълнителни данни:**
- `customer_logged` → boolean статус на потребителя за шаблона

### 5. Функционалност за количката

**Метод:** `prepareCartData()`
```php
private function prepareCartData() {
    $cartCount = 0;
    
    // Проверяваме дали cart обектът съществува и получаваме броя продукти
    if (isset($this->cart) && is_object($this->cart) && method_exists($this->cart, 'countProducts')) {
        $cartCount = $this->cart->countProducts();
    }
    
    // Предаваме броя продукти в количката към шаблона
    $this->data['cart_count'] = $cartCount;
}
```

**Особености:**
- Безопасна проверка на съществуването на `$this->cart` обекта
- Използва метода `countProducts()` от Theme25\Cart класа
- По подразбиране връща 0 ако няма продукти или обектът не съществува

### 6. Данни предавани към шаблона

Следните променливи са налични в Twig шаблона:

```php
$this->data = [
    // Основни URL адреси
    'account_url' => string,     // URL към профил или вход
    'wishlist_url' => string,    // URL към wishlist или вход  
    'cart_url' => string,        // URL към количка
    
    // Статус и данни
    'customer_logged' => bool,   // Дали потребителят е логнат
    'cart_count' => int,         // Брой продукти в количката
    
    // Останали съществуващи данни...
];
```

## Технически детайли

### Използвани OpenCart обекти:
- `$this->customer` - Customer обект за проверка на логнат потребител
- `$this->cart` - Cart обект за получаване на броя продукти

### Използвани методи:
- `$this->customer->isLogged()` - Проверка дали потребителят е логнат
- `$this->cart->countProducts()` - Получаване на броя продукти в количката
- `$this->getLink()` - Генериране на URL адреси

### Принципи на кодиране:
- **Single Responsibility Principle** - Всеки метод има една отговорност
- **Безопасност** - Проверка на съществуването на обектите преди използването им
- **Четимост** - Ясни имена на методите и добра документация
- **Поддръжка** - Разделяне на сложната логика в по-малки методи

## Резултат

Файлът `Header.php` сега предоставя пълна функционалност за:
- ✅ Проверка на статуса на потребителя (логнат/нелогнат)
- ✅ Динамично генериране на URL адреси според статуса
- ✅ Получаване на реалния брой продукти в количката
- ✅ Предаване на всички необходими данни към шаблона
- ✅ Спазване на принципите за разделяне на отговорностите

Кодът е готов за използване и тестване във Frontend частта на сайта.
