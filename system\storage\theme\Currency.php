<?php
namespace Theme25;

class Currency {
	private $currencies = array();
	private $db;
	private $language;
	private $envLoader;
	private $registry;
	private $priceDisplayFormat = null;

	/**
	 * Фиксиран курс BGN към EUR (1 EUR = 1.95583 BGN)
	 */
	const BGN_TO_EUR_RATE = 1.95583;

	public function __construct($registry) {
		$this->registry = $registry;
		$this->db = $registry->get('db');
		$this->language = $registry->get('language');

		// Зареждане на .env настройки
		$envPath = defined('DIR_THEME') ? DIR_THEME . '.env' : __DIR__ . '/.env';
		$this->envLoader = new EnvLoader($envPath);

		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "currency");

		foreach ($query->rows as $result) {
			$this->currencies[$result['code']] = array(
				'currency_id'   => $result['currency_id'],
				'title'         => $result['title'],
				'symbol_left'   => $result['symbol_left'],
				'symbol_right'  => $result['symbol_right'],
				'decimal_place' => $result['decimal_place'],
				'value'         => $result['value']
			);
		}
	}

	public function format($number, $currency='', $value = '', $format = true) {
		// Получаване на настройката за формат на изписване на цените
		$priceDisplayFormat = $this->getPriceDisplayFormatInternal();
		if (!$currency) {
			$priceDisplayFormat_parts = explode('-', $priceDisplayFormat);
			$currency = strtoupper($priceDisplayFormat_parts[0]);
		}

		// Форматиране според настройката
		switch ($priceDisplayFormat) {
			case 'bgn':
				// Показвай само цени в лева
				return $this->formatSingle($number, 'BGN', $value, $format);

			case 'eur':
				// Показвай само цени в евро
				return $this->formatSingle($number, 'EUR', $value, $format);

			case 'bgn-eur':
				// Показвай цени като "XX лв. / € YY"
				return $this->formatDualInternal($number, 'BGN', 'EUR', $value, $format);

			case 'eur-bgn':
				// Показвай цени като "€ XX / YY лв."
				return $this->formatDualInternal($number, 'EUR', 'BGN', $value, $format);

			default:
				// Fallback към стандартното поведение
				// Проверка дали е активиран режим за автоматично двойно форматиране
				if ($this->envLoader && $this->envLoader->get('DUAL_CURRENCY_AUTO_FORMAT', false)) {
					$primaryCurrency = $this->envLoader->get('DUAL_CURRENCY_PRIMARY', 'BGN');
					$secondaryCurrency = $this->envLoader->get('DUAL_CURRENCY_SECONDARY', 'EUR');

					// Ако текущата валута съвпада с основната валута, използваме двойно форматиране
					if ($currency === $primaryCurrency && $this->isDualCurrencySupported($primaryCurrency, $secondaryCurrency)) {
						return $this->formatDualInternal($number, $primaryCurrency, $secondaryCurrency, $value, $format);
					}
				}

				// Стандартно форматиране
				return $this->formatSingle($number, $currency, $value, $format);
		}
	}

	/**
	 * Вътрешен метод за стандартно форматиране на една валута
	 */
	private function formatSingle($number, $currency, $value = '', $format = true) {
		$symbol_left = $this->currencies[$currency]['symbol_left'];
		$symbol_right = $this->currencies[$currency]['symbol_right'];
		$decimal_place = $this->currencies[$currency]['decimal_place'];

		if (!$value) {
			$value = $this->currencies[$currency]['value'];
		}

		$amount = $value ? (float)$number * $value : (float)$number;

		$amount = round($amount, (int)$decimal_place);

		if (!$format) {
			return $amount;
		}

		$string = '';

		if ($symbol_left) {
			$string .= $symbol_left;
		}

		$string .= number_format($amount, (int)$decimal_place, '.', ',');

		if ($symbol_right) {
			$string .= $symbol_right;
		}

		return $string;
	}

	/**
	 * Форматира цена в двойни валути (BGN / EUR)
	 *
	 * @param float $number Сумата за форматиране
	 * @param string $primaryCurrency Основната валута (по подразбиране BGN)
	 * @param string $secondaryCurrency Втората валута (по подразбиране EUR)
	 * @param string $value Стойност за конвертиране (ако е различна от базовата)
	 * @param bool $format Дали да форматира резултата
	 * @return string Форматирана цена в двойни валути
	 */
	public function formatDual($number, $primaryCurrency = 'BGN', $secondaryCurrency = 'EUR', $value = '', $format = true) {
		return $this->formatDualInternal($number, $primaryCurrency, $secondaryCurrency, $value, $format);
	}

	/**
	 * Вътрешен метод за форматиране в двойни валути (избягва рекурсия)
	 */
	private function formatDualInternal($number, $primaryCurrency = 'BGN', $secondaryCurrency = 'EUR', $value = '', $format = true) {
		// Валидация на входните параметри
		if (!is_numeric($number)) {
			$number = 0;
		}

		// Проверка дали валутите съществуват в системата
		if (!isset($this->currencies[$primaryCurrency]) || !isset($this->currencies[$secondaryCurrency])) {
			// Ако някоя от валутите не съществува, използваме стандартния format метод
			return $this->formatSingle($number, $primaryCurrency, $value, $format);
		}

		// Форматиране на основната валута
		$primaryFormatted = $this->formatSingle($number, $primaryCurrency, $value, $format);

		// Конвертиране към втората валута
		$secondaryAmount = 0;
		if ($primaryCurrency === 'BGN' && $secondaryCurrency === 'EUR') {
			// Конвертиране от BGN към EUR с фиксиран курс
			//$secondaryAmount = (float)$number / self::BGN_TO_EUR_RATE;
			$secondaryAmount = (float)$number / self::BGN_TO_EUR_RATE;
			$value = 1;
		} elseif ($primaryCurrency === 'EUR' && $secondaryCurrency === 'BGN') {
			// Конвертиране от EUR към BGN с фиксиран курс
			$secondaryAmount = (float)$number * self::BGN_TO_EUR_RATE;
			$value = 1;
		} else {
			// За други валути използваме стандартната конвертация
			$secondaryAmount = $this->convert($number, $primaryCurrency, $secondaryCurrency);
		}

		// Форматиране на втората валута
		$secondaryFormatted = $this->formatSingle($secondaryAmount, $secondaryCurrency, $value, $format);

		// Връщане на комбинирания резултат
		return $primaryFormatted . ' / ' . $secondaryFormatted;
	}

	public function convert($value, $from, $to) {
		if (isset($this->currencies[$from])) {
			$from = $this->currencies[$from]['value'];
		} else {
			$from = 1;
		}

		if (isset($this->currencies[$to])) {
			$to = $this->currencies[$to]['value'];
		} else {
			$to = 1;
		}

		return $value * ($to / $from);
	}
	
	public function getId($currency) {
		if (isset($this->currencies[$currency])) {
			return $this->currencies[$currency]['currency_id'];
		} else {
			return 0;
		}
	}

	public function getSymbolLeft($currency) {
		if (isset($this->currencies[$currency])) {
			return $this->currencies[$currency]['symbol_left'];
		} else {
			return '';
		}
	}

	public function getSymbolRight($currency) {
		if (isset($this->currencies[$currency])) {
			return $this->currencies[$currency]['symbol_right'];
		} else {
			return '';
		}
	}

	public function getDecimalPlace($currency) {
		if (isset($this->currencies[$currency])) {
			return $this->currencies[$currency]['decimal_place'];
		} else {
			return 0;
		}
	}

	public function getValue($currency) {
		if (isset($this->currencies[$currency])) {
			return $this->currencies[$currency]['value'];
		} else {
			return 0;
		}
	}

	public function has($currency) {
		return isset($this->currencies[$currency]);
	}

	/**
	 * Конвертира сума от BGN към EUR с фиксиран курс
	 *
	 * @param float $amount Сумата в BGN
	 * @return float Сумата в EUR
	 */
	public function convertBgnToEur($amount) {
		return (float)$amount / self::BGN_TO_EUR_RATE;
	}

	/**
	 * Конвертира сума от EUR към BGN с фиксиран курс
	 *
	 * @param float $amount Сумата в EUR
	 * @return float Сумата в BGN
	 */
	public function convertEurToBgn($amount) {
		return (float)$amount * self::BGN_TO_EUR_RATE;
	}

	/**
	 * Връща фиксирания курс BGN към EUR
	 *
	 * @return float Курса BGN към EUR
	 */
	public function getBgnToEurRate() {
		return self::BGN_TO_EUR_RATE;
	}

	/**
	 * Проверява дали двете валути са поддържани за двойно показване
	 *
	 * @param string $currency1 Първата валута
	 * @param string $currency2 Втората валута
	 * @return bool True ако двете валути са поддържани
	 */
	public function isDualCurrencySupported($currency1, $currency2) {
		$supportedPairs = [
			['BGN', 'EUR'],
			['EUR', 'BGN']
		];

		foreach ($supportedPairs as $pair) {
			if (($currency1 === $pair[0] && $currency2 === $pair[1]) ||
				($currency1 === $pair[1] && $currency2 === $pair[0])) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Проверява дали е активиран режим за автоматично двойно форматиране
	 *
	 * @return bool True ако е активиран автоматичния режим
	 */
	public function isAutoFormatEnabled() {
		return $this->envLoader && $this->envLoader->get('DUAL_CURRENCY_AUTO_FORMAT', false);
	}

	/**
	 * Връща основната валута от .env настройките
	 *
	 * @return string Кода на основната валута
	 */
	public function getPrimaryCurrency() {
		return $this->envLoader ? $this->envLoader->get('DUAL_CURRENCY_PRIMARY', 'BGN') : 'BGN';
	}

	/**
	 * Връща втората валута от .env настройките
	 *
	 * @return string Кода на втората валута
	 */
	public function getSecondaryCurrency() {
		return $this->envLoader ? $this->envLoader->get('DUAL_CURRENCY_SECONDARY', 'EUR') : 'EUR';
	}

	/**
	 * Получава настройката за формат на изписване на цените (публичен метод)
	 *
	 * @return string Формата за изписване на цените ('bgn', 'eur', 'bgn-eur', 'eur-bgn')
	 */
	public function getPriceDisplayFormat() {
		return $this->getPriceDisplayFormatInternal();
	}

	/**
	 * Получава настройката за формат на изписване на цените от базата данни (вътрешен метод)
	 *
	 * @return string Формата за изписване на цените ('bgn', 'eur', 'bgn-eur', 'eur-bgn')
	 */
	private function getPriceDisplayFormatInternal() {
		// Кеширане на настройката за да не се чете многократно от базата данни
		if ($this->priceDisplayFormat === null) {
			try {
				// Използване на CommonMethods за четене на конфигурационната настройка
				$commonMethods = \Theme25\CommonMethods::getInstance($this->registry);
				$this->priceDisplayFormat = $commonMethods->getConfig('config_price_display_format', 'bgn');

				// Валидация на стойността
				$validFormats = ['bgn', 'eur', 'bgn-eur', 'eur-bgn'];
				if (!in_array($this->priceDisplayFormat, $validFormats)) {
					$this->priceDisplayFormat = 'bgn'; // Fallback към стойност по подразбиране
				}
			} catch (Exception $e) {
				// При грешка използваме стойност по подразбиране
				$this->priceDisplayFormat = 'bgn';
			}
		}

		return $this->priceDisplayFormat;
	}

	/**
	 * Принудително обновява кешираната настройка за формат на изписване на цените
	 * Полезно след промяна на настройката в административния панел
	 *
	 * @return void
	 */
	public function refreshPriceDisplayFormat() {
		$this->priceDisplayFormat = null;
		$this->getPriceDisplayFormatInternal(); // Презарежда настройката
	}

	/**
	 * Форматира цена с конкретен формат, заобикаляйки настройката от базата данни
	 *
	 * @param float $number Сумата за форматиране
	 * @param string $displayFormat Формата за изписване ('bgn', 'eur', 'bgn-eur', 'eur-bgn')
	 * @param string $value Стойност за конвертиране (ако е различна от базовата)
	 * @param bool $format Дали да форматира резултата
	 * @return string Форматирана цена
	 */
	public function formatWithSpecificFormat($number, $displayFormat, $value = '', $format = true) {
		switch ($displayFormat) {
			case 'bgn':
				return $this->formatSingle($number, 'BGN', $value, $format);

			case 'eur':
				return $this->formatSingle($number, 'EUR', $value, $format);

			case 'bgn-eur':
				return $this->formatDualInternal($number, 'BGN', 'EUR', $value, $format);

			case 'eur-bgn':
				return $this->formatDualInternal($number, 'EUR', 'BGN', $value, $format);

			default:
				// При невалиден формат използваме стандартното форматиране
				return $this->formatSingle($number, 'BGN', $value, $format);
		}
	}
}
