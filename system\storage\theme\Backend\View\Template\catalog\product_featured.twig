{% set selected = selected_products|default([]) %}
<div class="p-6">
  <div class="flex items-center justify-between mb-6">
    <h1 class="text-2xl font-semibold">Предложени продукти</h1>
    <a href="{{ back_url }}" class="text-primary hover:underline">Назад</a>
  </div>

  <div class="bg-white rounded shadow p-4">
    <p class="mb-4 text-gray-700">Изберете точно 4 продукта за показване на началната страница.</p>

    <div class="mb-4">
      <label class="block text-sm font-medium mb-1">Търсене на продукт</label>
      <div id="featured-search-wrapper" class="relative">
        <input type="text" id="featured-search" class="border rounded px-3 py-2 w-full" placeholder="Въведете име на продукт...">
        <div id="featured-search-results" class="mt-2 border rounded hidden bg-white shadow"></div>
      </div>
    </div>

    <div style="position: relative;">
      <label class="block text-sm font-medium mb-1">Избрани продукти (<span id="featured-selected-count">{{ selected|length }}</span>)</label>
      <div id="featured-selected" class="flex flex-col gap-2">
        {% for p in selected %}
          <div class="featured-selected-item flex items-center justify-between rounded px-3 py-2 bg-gray-100 border border-gray-200" data-id="{{ p.product_id }}" draggable="true">
            <div class="flex items-center gap-3">
              <span class="drag-handle cursor-move select-none text-gray-400" title="Премести">⋮⋮</span>
              {% if p.thumb %}<img src="{{ p.thumb }}" alt="" class="w-10 h-10 rounded object-cover">{% endif %}
              <div class="leading-tight">
                <div class="font-medium text-gray-800">
                  {% if p.edit_link %}
                    <a href="{{ p.edit_link }}" class="product-name-link" title="Редактирай продукт">{{ p.name }}</a>
                  {% else %}
                    {{ p.name }}
                  {% endif %}
                </div>
                {% if p.model %}<div class="product-model">Код: {{ p.model }}</div>{% endif %}
                {% if p.price_formatted %}
                  <div class="product-price mt-1">{{ p.price_formatted }}</div>
                {% else %}
                  <div class="product-price no-price mt-1">Няма цена</div>
                {% endif %}
              </div>
            </div>
            <button class="remove-selected w-8 h-8 flex items-center justify-center text-gray-500 hover:text-red-600" data-id="{{ p.product_id }}" aria-label="Премахни">
              <span aria-hidden="true">✕</span>
            </button>
          </div>
        {% endfor %}
      </div>
      <p class="text-xs text-gray-500 mt-2">Задръжте върху дръжката ⋮⋮ и влачете, за да пренаредите.</p>
    </div>

    <div class="mt-6">
      <button id="featured-save" class="bg-primary text-white px-6 py-2 rounded">Запази</button>
    </div>
  </div>
</div>

<script>
  window.ProductFeaturedConfig = {
    submitUrl: '{{ submit_url }}',
    userToken: '{{ user_token|default('') }}'
  };
</script>

