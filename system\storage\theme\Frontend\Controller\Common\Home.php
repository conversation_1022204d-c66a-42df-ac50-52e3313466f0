<?php

namespace Theme25\Frontend\Controller\Common;

class Home extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'common/home');
    }

	public function index() {
		$this->setTitle($this->getConfig('config_name'));
		$this->addFrontendStyleWithVersion('swiper-bundle.min.css');
		$this->addFrontendScriptWithVersion('swiper-bundle.min.js', 'footer');

		// $this->setUnderConstructionWhenIsNotDeveloper(); // TODO: Да го махна, когато приключи разработката

		$hero_html = $this->loadController('theme/hero_banner');
		$this->setData('hero_banner_html', $hero_html);

		$popular_categories_html = $this->getPopularCategories();
		$this->setData('popular_categories_html', $popular_categories_html);

		// Featured products (динамични)
		$this->prepareFeaturedProducts();

		// $promo_products_html = $this->loadController('theme/promo_products');
		// $this->setData('promo_products_html', $promo_products_html);

		// $collections_html = $this->loadController('theme/collections');
		// $this->setData('collections_html', $collections_html);

		$testimonials = true;
		$this->setData('testimonials', $testimonials);

		$benefits = true;
		$this->setData('benefits', $benefits);

		$this->renderTemplateWithDataAndOutput('common/home');
	}

	private function getPopularCategories() {

		$data = [];
		// .... в бъдеще ще може да се настройват от админ панела

		return $this->renderPartTemplate('common/home/<USER>', $data);
	}

	/**
	 * Подготвя Featured Products секцията.
	 * - Зарежда от таблица oc_product_featured (ако съществува и има стойности)
	 * - Ако няма, fallback към 4 най-продавани (best sellers)
	 * - Рендира partial шаблон и запазва HTML в featured_products_html
	 */
	private function prepareFeaturedProducts() {
		$limit = 4;
		$usedFallback = false;
		$section_title = 'Предложени продукти';

		// Зареждаме нужните модели
		$this->loadModelsAs([
			'catalog/product' => 'productModel',
			'tool/image' => 'imageModel'
		]);

		// Зареждаме глобалния модел за предложени продукти
		$this->loadModelAs('catalog/product/featured', 'featuredModel');

		// Получаваме ID-та на предложените продукти
		$featuredIds = $this->featuredModel->getFeaturedProductIds($limit);

		$products = [];

		if (!empty($featuredIds)) {
			foreach ($featuredIds as $pid) {
				$info = $this->productModel->getProduct($pid);
				if (!$info) continue;
				$products[] = $this->prepareProductCardData($info, false);
			}
		} else {
			$usedFallback = true;
			$section_title = 'Най-продавани продукти';
			$bestsellers = $this->productModel->getBestSellerProducts($limit);
			foreach ($bestsellers as $info) {
				// Преизвикваме getProduct за пълни данни ако е налично product_id
				if (isset($info['product_id'])) {
					$full = $this->productModel->getProduct((int)$info['product_id']);
					if ($full) $info = array_merge($info, $full);
				}
				$products[] = $this->prepareProductCardData($info, true);
			}
		}

		// Рендираме partial-а
		$featured_products_html = '';
		if (!empty($products)) {
			$featured_products_html = $this->renderPartTemplate('common/home/<USER>', [
				'products' => $products,
				'section_title' => $section_title
			]);
		}

		$this->setData('featured_products_html', $featured_products_html);
	}

	/**
	 * Подготвя данните за една продуктова карта за шаблона
	 */
	private function prepareProductCardData(array $productInfo, $forceBestseller = false) {
		$name = $productInfo['name'] ?? '';
		$image = $productInfo['image'] ?? '';
		$price = isset($productInfo['price']) ? (float)$productInfo['price'] : 0.0;
		$special = isset($productInfo['special']) && $productInfo['special'] !== null && $productInfo['special'] !== false
			? (float)$productInfo['special'] : null;
		$product_id = (int)($productInfo['product_id'] ?? 0);
		$href = $this->getLink('product/product', 'product_id=' . $product_id);

		$thumb = '';
		if ($image) {
			try {
				$thumb = $this->imageModel->resize($image, 600, 600);
			} catch (\Exception $e) {
				$thumb = ThemeData()->getImageServerUrl() . 'placeholder.png';
			}
		}

		$labels = \Theme25\ProductLabels::getLabels([
			'price' => $price,
			'special' => $special,
			'date_added' => $productInfo['date_added'] ?? null
		], ['force_bestseller' => $forceBestseller]);

		return [
			'product_id' => $product_id,
			'name' => $name,
			'href' => $href,
			'thumb' => $thumb,
			'price' => $price,
			'special' => $special,
			'price_formatted' => $this->formatCurrency($price),
			'special_formatted' => $special !== null ? $this->formatCurrency($special) : null,
			'labels' => $labels
		];
	}
}
