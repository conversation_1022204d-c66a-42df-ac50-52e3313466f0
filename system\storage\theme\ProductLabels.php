<?php

namespace Theme25;

/**
 * Клас за централизирано управление на продуктовите етикети (badges)
 * - ново
 * - намаление (процент)
 * - топ продажби
 * - наличност/ограничено количество (опционално за бъдещи разширения)
 */
class ProductLabels
{
    /**
     * Връща масив от етикети за даден продукт
     * Всеки етикет е асоциативен масив: ['type' => 'new|discount|bestseller', 'text' => '...', 'class' => '...']
     *
     * @param array $product Информация за продукта (очаква ключове като price, special, date_added и др.)
     * @param array $options Допълнителни опции (напр. ['force_bestseller' => true])
     * @return array
     */
    public static function getLabels(array $product, array $options = [])
    {
        $labels = [];

        // Намаление: ако има специална цена по-ниска от базовата
        $price = isset($product['price']) ? (float)$product['price'] : 0.0;
        $special = isset($product['special']) && $product['special'] !== null && $product['special'] !== false
            ? (float)$product['special'] : null;
        if ($price > 0 && $special !== null && $special >= 0 && $special < $price) {
            $discountPercent = (int)round((1 - ($special / $price)) * 100);
            if ($discountPercent > 0) {
                $labels[] = [
                    'type' => 'discount',
                    'text' => '-' . $discountPercent . '%',
                    'class' => 'bg-red-600 text-white text-xs px-2 py-1 rounded'
                ];
            }
        }

        // Нов продукт: ако е добавен последните N дни (напр. 30)
        $daysForNew = 30;
        if (!empty($product['date_added'])) {
            $dateAddedTs = strtotime($product['date_added']);
            if ($dateAddedTs) {
                $daysDiff = floor((time() - $dateAddedTs) / 86400);
                if ($daysDiff >= 0 && $daysDiff <= $daysForNew) {
                    $labels[] = [
                        'type' => 'new',
                        'text' => 'Ново',
                        'class' => 'bg-green-600 text-white text-xs px-2 py-1 rounded'
                    ];
                }
            }
        }

        // Топ продажби: ако е зададено от извикващия (например при fallback към бестселъри)
        if (!empty($options['force_bestseller'])) {
            $labels[] = [
                'type' => 'bestseller',
                'text' => 'Топ продажба',
                'class' => 'bg-yellow-500 text-black text-xs px-2 py-1 rounded'
            ];
        }

        return $labels;
    }
}

