<?php

namespace Theme25\Frontend\Controller\Common;

class Header extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'common/header');
    }

    public function index() {

        // Подготовка на данните с верижно извикване на методи
        $this->prepareBasicData()
             ->prepareNavigationData()
             ->prepareUserData()
             ->prepareMenuData();

        // Рендиране на шаблона с данните от $this->data
        $content = $this->loadView('common/header', $this->data);

        if(defined('STANDART_VIEW')) {
            return $content . '<div class="standart-view m-auto">';
        }
        
        return $content;
    }

    /**
     * Подготовка на основните данни за header-а
     */
    private function prepareBasicData() {
        // Logo и основни URL адреси
        $this->data['home_url'] = $this->getUrl('common/home');
        $this->data['logo_url'] = 'https://www.rakla.bg/image/rakla-logo.svg';
        $this->data['site_name'] = $this->getConfig('config_name', 'Ракла');

        return $this;
    }

    /**
     * Подготовка на данните за навигация
     */
    private function prepareNavigationData() {
        // Search functionality
        $this->data['search_placeholder'] = 'Търсене...';
        $this->data['ask_betty_text'] = 'Питай Бети';

        return $this;
    }

    /**
     * Подготовка на данните за потребителя
     */
    private function prepareUserData() {
        // Проверка дали потребителят е логнат
        $isCustomerLogged = $this->isCustomerLogged();

        // Подготовка на URL адресите според статуса на потребителя
        $this->prepareUserUrls($isCustomerLogged);

        // Подготовка на данните за количката
        $this->prepareCartData();

        return $this;
    }

    /**
     * Проверява дали потребителят е логнат във Frontend частта
     *
     * @return bool True ако потребителят е логнат
     */
    private function isCustomerLogged() {
        // Проверяваме дали customer обектът съществува и дали потребителят е логнат
        if (isset($this->customer) && is_object($this->customer) && method_exists($this->customer, 'isLogged')) {
            return $this->customer->isLogged();
        }

        return false;
    }

    /**
     * Подготвя URL адресите според статуса на потребителя
     *
     * @param bool $isLogged Дали потребителят е логнат
     */
    private function prepareUserUrls($isLogged) {
        if ($isLogged) {
            // Потребителят е логнат - показваме линкове към профил и wishlist
            $this->data['account_url'] = $this->getLink('account/account');
            $this->data['wishlist_url'] = $this->getLink('account/wishlist');
        } else {
            // Потребителят НЕ е логнат - пренасочваме към страницата за вход
            $this->data['account_url'] = $this->getLink('account/login');
            $this->data['wishlist_url'] = $this->getLink('account/login');
        }

        // URL към количката е винаги достъпен
        $this->data['cart_url'] = $this->getLink('checkout/cart');

        // Предаваме информацията за статуса на потребителя към шаблона
        $this->data['customer_logged'] = $isLogged;
    }

    /**
     * Подготвя данните за количката
     */
    private function prepareCartData() {
        $cartCount = 0;
        $cartItems = [];
        $cartTotal = 0;
        $cartTotalFormatted = '';

        // Проверяваме дали cart обектът съществува
        if (isset($this->cart) && is_object($this->cart)) {
            // Получаваме броя продукти
            if (method_exists($this->cart, 'countProducts')) {
                $cartCount = $this->cart->countProducts();
            }

            // Получаваме продуктите в количката
            if (method_exists($this->cart, 'getProducts')) {
                $products = $this->cart->getProducts();

                // Зареждаме модела за изображения
                $this->loadModelAs('tool/image', 'imageModel');

                foreach ($products as $product) {
                    // Подготвяме изображението на продукта
                    $image = '';
                    if ($product['image']) {
                        $image = $this->imageModel->resize($product['image'], 80, 80);
                    } else {
                        $image = $this->imageModel->resize('placeholder.png', 80, 80);
                    }

                    // Подготвяме данните за продукта
                    $cartItems[] = [
                        'cart_id' => $product['cart_id'],
                        'product_id' => $product['product_id'],
                        'name' => $product['name'],
                        'model' => $product['model'],
                        'image' => $image,
                        'quantity' => $product['quantity'],
                        'price' => $this->formatCurrency($product['price']),
                        'total' => $this->formatCurrency($product['total']),
                        'size' => isset($product['option']) && !empty($product['option']) ? $this->formatOptions($product['option']) : '',
                        'remove_url' => $this->getLink('checkout/cart', 'remove=' . $product['cart_id'])
                    ];
                }
            }

            // Получаваме общата сума на количката
            if (method_exists($this->cart, 'getTotal')) {
                $cartTotal = $this->cart->getTotal();
                $cartTotalFormatted = $this->formatCurrency($cartTotal);
            }
        }

        // Предаваме всички данни към шаблона
        $this->data['cart_count'] = $cartCount;
        $this->data['cart_items'] = $cartItems;
        $this->data['cart_total'] = $cartTotal;
        $this->data['cart_total_formatted'] = $cartTotalFormatted;
    }

    /**
     * Форматира опциите на продукта за показване
     *
     * @param array $options Опциите на продукта
     * @return string Форматиран текст с опциите
     */
    private function formatOptions($options) {
        $optionTexts = [];

        if (is_array($options)) {
            foreach ($options as $option) {
                if (isset($option['name']) && isset($option['value'])) {
                    $optionTexts[] = $option['name'] . ': ' . $option['value'];
                }
            }
        }

        return implode(', ', $optionTexts);
    }

    private function prepareMenuData() {
        $this->data['menu_data'] = $this->loadController('common/mega_menu/getMegaMenuItems');
        return $this;
    }

}