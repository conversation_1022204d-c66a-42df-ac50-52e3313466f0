<?php

namespace Theme25\Backend\Controller\System\Theme;

/**
 * Суб-контролер за автодопълване на страници
 *
 * @package Theme25\Backend\Controller\System\Theme
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class PagesAutocomplete extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->loadModels();
    }

    /**
     * Зареждане на необходимите модели
     */
    private function loadModels() {
        $this->loadModelAs('catalog/information', 'informationModel');
        $this->loadModelAs('catalog/category', 'categoryModel');
        $this->loadModelAs('catalog/product', 'productModel');
    }

    /**
     * Автодопълване за страници, категории и продукти
     */
    public function autocomplete($params) {
        $searchTerm = isset($params['filter_name']) ? trim($params['filter_name']) : '';
        $filterType = isset($params['filter_type']) ? trim($params['filter_type']) : 'all'; // Нов параметър за тип
        $limit = isset($params['limit']) ? (int)$params['limit'] : 25;
        $offset = max(0, (int)($params['offset'] ?? 0));

        // Проверка за backward compatibility
        $useNewFormat = isset($params['offset']);

        // Поддръжка за различни параметри за търсене
        if(!isset($params['filter_name']) && isset($params['q'])) {
            $searchTerm = trim($params['q']);
        }

        // Ако няма filter_type, но има q параметър, използваме 'page' като default
        if ($filterType === 'all' && isset($params['q'])) {
            $filterType = 'page';
        }

        // Ако няма търсен термин, връщаме според типа
        if (empty($searchTerm)) {
            return $this->getDefaultResultsByType($filterType, $limit, $offset, $useNewFormat);
        }

        // Търсене според заявения тип
        $allResults = $this->searchByType($searchTerm, $filterType);

        // Сортиране по релевантност
        $sortedResults = $this->sortByRelevance($allResults, $searchTerm);

        // Изчисляване на общия брой резултати
        $totalCount = count($sortedResults);

        // Прилагане на пагинацията
        $paginatedResults = array_slice($sortedResults, $offset, $limit);

        // Определяне дали има още резултати
        $hasMore = ($offset + $limit) < $totalCount;

        // Връщане на резултатите в подходящия формат
        if ($useNewFormat) {
            return [
                'results' => $paginatedResults,
                'has_more' => $hasMore,
                'total_count' => $totalCount,
                'current_offset' => $offset
            ];
        } else {
            // Backward compatibility - връщаме само резултатите
            return $paginatedResults;
        }
    }

    /**
     * Получаване на информационни страници без търсене
     */
    private function getInformationPages($limit = 25, $offset = 0, $useNewFormat = false) {
        $json = [];

        $filter_data = [
            'filter_status' => 1,
            'start' => $offset,
            'limit' => $limit
        ];

        $pages = $this->informationModel->getInformations($filter_data);

        foreach ($pages as $page) {
            $json[] = [
                'name' => '(Страница) ' . $page['title'],
                'value' => $this->getPageUrl($page['information_id']),
                'type' => 'page'
            ];
        }

        if ($useNewFormat) {
            // Получаване на общия брой страници за изчисляване на has_more
            $totalCount = $this->informationModel->getTotalInformations();
            $hasMore = ($offset + $limit) < $totalCount;

            return [
                'results' => $json,
                'has_more' => $hasMore,
                'total_count' => $totalCount,
                'current_offset' => $offset
            ];
        }

        return $json;
    }

    /**
     * Търсене в информационни страници
     */
    private function searchInformationPages($searchTerm, $limit = 10, $offset = 0) {
        $json = [];

        $results = $this->informationModel->searchInformations($searchTerm, $limit, $offset);

        foreach ($results as $page) {
            $json[] = [
                'name' => '(Страница) ' . $page['name'],
                'value' => $this->getPageUrl($page['id']),
                'type' => 'page',
                'relevance' => $this->calculateRelevance($page['name'], $searchTerm)
            ];
        }

        return $json;
    }

    /**
     * Търсене в категории
     */
    private function searchCategories($searchTerm, $limit = 10, $offset = 0) {
        $json = [];

        $results = $this->categoryModel->searchCategories($searchTerm, $limit, $offset);

        foreach ($results as $category) {
            $json[] = [
                'name' => '(Категория) ' . strip_tags(html_entity_decode($category['name'], ENT_QUOTES, 'UTF-8')),
                'value' => $this->getCategoryUrl($category['id']),
                'type' => 'category',
                'relevance' => $this->calculateRelevance($category['name'], $searchTerm)
            ];
        }

        return $json;
    }

    /**
     * Търсене в продукти
     */
    private function searchProducts($searchTerm, $limit = 10) {
        $json = [];

        $results = $this->productModel->searchProducts($searchTerm, $limit);

        foreach ($results as $product) {
            $json[] = [
                'name' => '(Продукт) ' . $product['name'],
                'value' => $this->getProductUrl($product['id']),
                'type' => 'product',
                'relevance' => $this->calculateRelevance($product['name'], $searchTerm)
            ];
        }

        return $json;
    }

    /**
     * Генериране на URL за страница
     */
    private function getPageUrl($information_id) {
        return 'index.php?route=information/information&information_id=' . $information_id;
    }

    /**
     * Генериране на URL за категория
     */
    private function getCategoryUrl($category_id) {
        return 'index.php?route=catalog/category&category_id=' . $category_id;
    }

    /**
     * Генериране на URL за продукт
     */
    private function getProductUrl($product_id) {
        return 'index.php?route=catalog/product&product_id=' . $product_id;
    }

    /**
     * Изчисляване на релевантност на резултата
     */
    private function calculateRelevance($text, $searchTerm) {
        $text = mb_strtolower($text);
        $searchTerm = mb_strtolower($searchTerm);

        // Точно съвпадение - най-висока релевантност
        if ($text === $searchTerm) {
            return 100;
        }

        // Започва с търсения термин - висока релевантност
        if (mb_strpos($text, $searchTerm) === 0) {
            return 90;
        }

        // Съдържа търсения термин - средна релевантност
        if (mb_strpos($text, $searchTerm) !== false) {
            return 70;
        }

        // Съдържа думи от търсения термин - ниска релевантност
        $searchWords = explode(' ', $searchTerm);
        $matchedWords = 0;

        foreach ($searchWords as $word) {
            if (mb_strpos($text, $word) !== false) {
                $matchedWords++;
            }
        }

        if ($matchedWords > 0) {
            return 50 + ($matchedWords / count($searchWords)) * 20;
        }

        return 0;
    }

    /**
     * Сортиране на резултатите по релевантност с подобрено групиране
     */
    private function sortByRelevance($results, $searchTerm) {
        usort($results, function($a, $b) {
            // Първо сортираме по релевантност (по-висока релевантност отгоре)
            $relevanceDiff = $b['relevance'] - $a['relevance'];

            // Ако разликата в релевантността е значителна (>20), използваме я
            if (abs($relevanceDiff) > 20) {
                return $relevanceDiff;
            }

            // При близка релевантност, групираме по тип
            $typeOrder = ['page' => 1, 'category' => 2, 'product' => 3];
            $aOrder = isset($typeOrder[$a['type']]) ? $typeOrder[$a['type']] : 4;
            $bOrder = isset($typeOrder[$b['type']]) ? $typeOrder[$b['type']] : 4;

            if ($aOrder !== $bOrder) {
                return $aOrder - $bOrder;
            }

            // При еднакъв тип, сортираме по релевантност
            if ($relevanceDiff !== 0) {
                return $relevanceDiff;
            }

            // При еднакъв тип и релевантност, сортираме по име
            return strcmp($a['name'], $b['name']);
        });

        // Премахваме полето relevance от крайния резултат, но запазваме type за debugging
        foreach ($results as &$result) {
            unset($result['relevance']);
            // Временно запазваме type за по-добро debugging
            // unset($result['type']);
        }

        return $results;
    }

    /**
     * Групиране на резултатите по тип (алтернативен метод)
     */
    private function groupResultsByType($results) {
        $grouped = [
            'pages' => [],
            'categories' => [],
            'products' => []
        ];

        foreach ($results as $result) {
            switch ($result['type']) {
                case 'page':
                    $grouped['pages'][] = $result;
                    break;
                case 'category':
                    $grouped['categories'][] = $result;
                    break;
                case 'product':
                    $grouped['products'][] = $result;
                    break;
            }
        }

        // Обединяваме групите в правилния ред
        $finalResults = [];
        $finalResults = array_merge($finalResults, $grouped['pages']);
        $finalResults = array_merge($finalResults, $grouped['categories']);
        $finalResults = array_merge($finalResults, $grouped['products']);

        return $finalResults;
    }

    /**
     * Получаване на резултати по подразбиране според типа
     */
    private function getDefaultResultsByType($filterType, $limit, $offset, $useNewFormat) {
        switch ($filterType) {
            case 'page':
                return $this->getInformationPages($limit, $offset, $useNewFormat);
            case 'category':
                return $this->getCategories($limit, $offset, $useNewFormat);
            case 'product':
                return $this->getProducts($limit, $offset, $useNewFormat);
            case 'all':
            default:
                // За backward compatibility връщаме информационни страници
                return $this->getInformationPages($limit, $offset, $useNewFormat);
        }
    }

    /**
     * Търсене според заявения тип
     */
    private function searchByType($searchTerm, $filterType) {
        $results = [];

        switch ($filterType) {
            case 'page':
                $results = $this->searchInformationPages($searchTerm, 1000, 0);
                break;
            case 'category':
                $results = $this->searchCategories($searchTerm, 1000, 0);
                break;
            case 'product':
                $results = $this->searchProducts($searchTerm, 1000, 0);
                break;
            case 'all':
            default:
                // Търсене във всички типове (стара логика)
                $informationResults = $this->searchInformationPages($searchTerm, 1000, 0);
                $categoryResults = $this->searchCategories($searchTerm, 1000, 0);
                $productResults = $this->searchProducts($searchTerm, 1000, 0);
                $results = array_merge($informationResults, $categoryResults, $productResults);
                break;
        }

        return $results;
    }

    /**
     * Получаване на категории по подразбиране
     */
    private function getCategories($limit = 25, $offset = 0, $useNewFormat = false) {
        $filter_data = [
            'sort' => 'name',
            'order' => 'ASC',
            'start' => $offset,
            'limit' => $limit
        ];

        $categories = $this->categoryModel->getCategories($filter_data);
        $results = [];

        foreach ($categories as $category) {
            $results[] = [
                'name' => '(Категория) ' . $category['name'],
                'value' => 'index.php?route=catalog/category&category_id=' . $category['category_id'],
                'type' => 'category'
            ];
        }

        if ($useNewFormat) {
            $totalCategories = $this->categoryModel->getTotalCategories();
            return [
                'results' => $results,
                'has_more' => ($offset + $limit) < $totalCategories,
                'total_count' => $totalCategories,
                'current_offset' => $offset
            ];
        }

        return $results;
    }

    /**
     * Получаване на продукти по подразбиране
     */
    private function getProducts($limit = 25, $offset = 0, $useNewFormat = false) {
        $filter_data = [
            'sort' => 'pd.name',
            'order' => 'ASC',
            'start' => $offset,
            'limit' => $limit
        ];

        $products = $this->productModel->getProducts($filter_data);
        $results = [];

        foreach ($products as $product) {
            $results[] = [
                'name' => '(Продукт) ' . $product['name'],
                'value' => 'index.php?route=catalog/product&product_id=' . $product['product_id'],
                'type' => 'product'
            ];
        }

        if ($useNewFormat) {
            $totalProducts = $this->productModel->getTotalProducts();
            return [
                'results' => $results,
                'has_more' => ($offset + $limit) < $totalProducts,
                'total_count' => $totalProducts,
                'current_offset' => $offset
            ];
        }

        return $results;
    }
}
